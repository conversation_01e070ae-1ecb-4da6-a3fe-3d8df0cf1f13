lib-cov
*.seed
*.log
*.dat
*.out
*.pid
*.gz
*.swp

pids
logs
results
config/*
!config/local.sh
!config/local.example.js
build
build/Release
.eslintcache
dist
dist.zip
release
app/main.prod.js
app/main.prod.js.map
app/renderer.prod.js
app/renderer.prod.js.map
app/style.css
app/style.css.map
main.js
main.js.map


lib/drivers/medtronic/cli/*.json
lib/drivers/insulet/cli/*.ibf
resources/win/disk1
resources/win/setup.*

tmp
npm-debug.log
node_modules
app/node_modules
bower_components
coverage

.DS_Store
.idea
*.iml
.com.apple.timemachine.*
.tern-project

_book/
web/

\.vscode/

# yarn (ref https://yarnpkg.com/getting-started/qa#which-files-should-be-gitignored)
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/sdks
!.yarn/versions

app/.pnp.*
app/.yarn/*
!app/.yarn/patches
!app/.yarn/plugins
!app/.yarn/releases
!app/.yarn/sdks
!app/.yarn/versions
