<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
    <title>Tidepool Uploader</title>
    <script>
      (function() {
        if (!process.env.HOT) {
          const link = document.createElement('link');
          link.rel = 'stylesheet';
          link.href = './dist/style.css';
          // HACK: Writing the script path should be done with webpack
          document.getElementsByTagName('head')[0].appendChild(link);
        }
      }());
    </script>
  </head>
  <body>
    <div id="app"></div>
    <script>
      {
        // to enable `bows` logging we set debug here
        localStorage.setItem('debug', 'true');
        const script = document.createElement('script');
        const port = process.env.PORT || 3005;
        script.src = (process.env.HOT)
          ? 'http://localhost:' + port + '/dist/renderer.dev.js'
          : './dist/renderer.prod.js';
        document.body.appendChild(script);
      }
    </script>
  </body>
</html>
