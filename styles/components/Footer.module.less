/**
* Copyright (c) 2016, Tidepool Project
*
* This program is free software; you can redistribute it and/or modify it under
* the terms of the associated License, which is identical to the BSD 2-Clause
* License as published by the Open Source Initiative at opensource.org.
*
* This program is distributed in the hope that it will be useful, but WITHOUT
* ANY WARRANTY; without even the implied warranty of ME<PERSON>HANTABILITY or FITNESS
* FOR A PARTICULAR PURPOSE. See the License for more details.
*
* You should have received a copy of the License along with this program; if
* not, you can obtain one from Tidepool Project at tidepool.org.
*/

@import '../core/variables.less';

.footer {
  composes: flexColumn from '../core/layout.module.less';
  flex-shrink: 0;
  margin-top: auto;
  margin-bottom: 30px;
}

.footerRow {
  composes: flexRow from '../core/layout.module.less';
  width: 100%;
  max-width: 550px;
  margin: 0px auto;
  justify-content: space-around;
  align-self: flex-end;
  align-items: center;
}

.footerLink {
  composes: invertLink gray from '../core/typography.module.less';
  text-decoration: none;
  line-height: 36px;
  &:hover,
  &:focus,
  &:active {
    text-decoration: none;
  }
}

.version {
  composes: lightGray from '../core/typography.module.less';
  margin: 10px auto;
}

.betaWarning {
  composes: large from '../core/typography.module.less';
  color: @red-error;
  margin: 0px auto;
}
