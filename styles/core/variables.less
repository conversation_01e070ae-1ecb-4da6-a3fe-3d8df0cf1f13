/**
* Copyright (c) 2014, Tidepool Project
*
* This program is free software; you can redistribute it and/or modify it under
* the terms of the associated License, which is identical to the BSD 2-Clause
* License as published by the Open Source Initiative at opensource.org.
*
* This program is distributed in the hope that it will be useful, but WITHOUT
* ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS
* FOR A PARTICULAR PURPOSE. See the License for more details.
*
* You should have received a copy of the License along with this program; if
* not, you can obtain one from Tidepool Project at tidepool.org.
*/

// Colors
// ====================================

@purple-light:           rgba(96, 120, 255, 0.2); // button disabled
@purple-medium:          #627cff; // button normal, focus form border
@purple-dark:            #291448; // button hover, normal text

@red:                    rgb(247, 45, 45);
@green:                  #00754E;

@gray-light:             #bcbec0; // sec button border, form border disabled
@gray-medium:            #979797; // text disabled
@gray-background:        #f7fafa; // secondary botton fill
@red-error:              #ff3631;


// Scaffolding
// ====================================

@body-bg:                #f7f7f8;
@text-color:             @purple-dark;

@link-color:             @purple-medium;
@link-hover-color:       @purple-dark;

// Typography
// ====================================

@font-family-base:       "Basis", "Open Sans", "Helvetica Neue", Helvetica, Arial, sans-serif;

@font-size-tiny:         12px;
@line-height-tiny:       17px;

@font-size-small:        14px;
@line-height-small:      19px;

@font-size-medium:         16px;
@line-height-medium:       22px;

@font-size-large:        20px;
@line-height-large:      27px;


// Dimensions
// ====================================

@mainPageBorder: 1px;

// defined as the app height in main.js
@app-height: 710px;
@header-height: 110px;
@footer-height: 50px;
@page-height: @app-height - @header-height - @footer-height;
@usable-page-height: @page-height - 100px;
@groups-dropdown-height: 67px;
@timezone-dropdown-height: 85px;
@timezone-error-dropdown-height: 107px;
@changePerson-link-height: 32px;
@clinic-user-block-height: 68px;
@clinic-user-block-timezone-height: 143px;


// Form controls
// ====================================

@form-control-border-size:   2px;
@form-control-height:        (30px + 2*@form-control-border-size);
@form-control-border-color:  @gray-light;

// Buttons

@btn-border-size: 2px;
@btn-height: (30px + 2*@btn-border-size);
