/**
* Copyright (c) 2014, Tidepool Project
*
* This program is free software; you can redistribute it and/or modify it under
* the terms of the associated License, which is identical to the BSD 2-Clause
* License as published by the Open Source Initiative at opensource.org.
*
* This program is distributed in the hope that it will be useful, but WITHOUT
* ANY WARRANTY; without even the implied warranty of ME<PERSON><PERSON>NTABILITY or FITNESS
* FOR A PARTICULAR PURPOSE. See the License for more details.
*
* You should have received a copy of the License along with this program; if
* not, you can obtain one from Tidepool Project at tidepool.org.
*/

@import 'variables.less';


.form-control-border {
  border: @form-control-border-size solid fade(@form-control-border-color, 50%);
  border-radius: 0px;

  &:hover {
    border-color: @purple-light;
  }

  &:focus {
    // Disable default WebKit focus style
    outline: 0;
    border-color: @purple-medium;
  }

  &:disabled {
    border-color: fade(@form-control-border-color, 70%);
    cursor: not-allowed;
    background-color: @gray-background;
  }
}

.form-control {
  display: block;

  font-size: @font-size-medium;
  height: @form-control-height;
  line-height: (@line-height-medium);
  vertical-align: middle;

  padding: 0 15px;

  .form-control-border();
  background: #fff;
  color: @text-color;

  textarea& {
    height: auto;
    line-height: @line-height-medium;
  }
}
