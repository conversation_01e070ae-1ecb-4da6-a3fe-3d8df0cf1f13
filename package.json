{"name": "tidepool-uploader", "version": "2.62.0", "engines": {"node": "20.14.0"}, "packageManager": "yarn@3.6.4", "description": "Tidepool Project Universal Uploader", "private": true, "main": "main.prod.js", "contributors": ["<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON> Quirk"], "license": "BSD-2-<PERSON><PERSON>", "repository": "tidepool-org/uploader", "scripts": {"av-whitelist": "node ./scripts/whitelisting/av-submit.js", "build-docs": "./scripts/update-gh-pages.sh", "serve-docs": "./node_modules/.bin/gitbook serve", "test": "cross-env NODE_ENV=test BABEL_DISABLE_CACHE=1 jest", "test-all": "npm run lint && npm run test && npm run build", "lint": "node ./node_modules/eslint/bin/eslint.js --cache --format=node_modules/eslint-formatter-pretty .", "lint-fix": "npm run lint -- --fix", "build-main": "yarn build-main-quiet --progress", "build-main-quiet": "cross-env NODE_ENV=production webpack --config webpack.config.main.prod.babel.mjs", "build-renderer": "yarn build-renderer-quiet --progress", "build-renderer-quiet": "cross-env NODE_ENV=production webpack --config webpack.config.renderer.prod.babel.mjs", "build": "concurrently \"yarn build-main\" \"yarn build-renderer\"", "build-quiet": "concurrently \"yarn build-main-quiet\" \"yarn build-renderer-quiet\"", "build-web": "cross-env NODE_ENV=production webpack --config webpack.config.web.dev.babel.mjs", "start": "cross-env NODE_ENV=production electron --no-sandbox ./app/main.prod.js", "postinstall": "electron-builder install-app-deps", "dev": "cross-env START_HOT=1 yarn start-renderer-dev", "dev-web": "cross-env HOT=1 NODE_ENV=development webpack serve --config webpack.config.web.dev.babel.mjs", "start-renderer-dev": "cross-env NODE_ENV=development webpack-dev-server --config webpack.config.renderer.dev.babel.mjs", "start-main-dev": "cross-env HOT=1 NODE_ENV=development electron --no-sandbox -r @babel/register ./app/main.dev.js", "server": "node server", "prepare-qa-build": "node -r @babel/register scripts/prepare-qa-build.js", "package": "yarn build-quiet && electron-builder -p always -c electron-builder-publish.js"}, "dependencies": {"@electron/remote": "2.1.2", "@emotion/react": "11.14.0", "@emotion/styled": "11.14.0", "@hot-loader/react-dom": "16.14.0", "@mui/icons-material": "5.16.7", "@mui/material": "5.16.7", "@react-keycloak/web": "3.4.0", "async": "2.6.4", "babyparse": "0.4.6", "ble-glucose": "0.9.0", "body-parser": "1.20.3", "bows": "1.7.2", "chrome-launcher": "0.15.2", "classnames": "2.5.1", "commander": "4.1.1", "connected-react-router": "6.9.3", "core-js": "2.6.12", "cp2102": "0.1.2", "cross-env": "7.0.3", "electron-is-dev": "2.0.0", "electron-log": "4.4.0", "electron-updater": "6.6.2", "es6-error": "4.1.1", "express": "4.21.2", "fast-safe-stringify": "2.1.1", "ftdi-js": "0.4.1", "helmet": "8.0.0", "history": "4.10.1", "i18n-iso-countries": "7.13.0", "i18next": "20.6.1", "i18next-fs-backend": "2.3.1", "iconv-lite": "0.6.3", "idb-keyval": "6.2.1", "identity-obj-proxy": "3.0.0", "immutability-helper": "3.1.1", "is-electron": "2.2.2", "keycloak-js": "22.0.5", "lodash": "4.17.21", "lzo-wasm": "0.0.4", "node-polyfill-webpack-plugin": "4.1.0", "oidc-client-ts": "3.1.0", "os-name": "4.0.1", "pako": "2.1.0", "pl2303": "0.1.0", "plist": "3.1.0", "prop-types": "15.8.1", "react": "16.14.0", "react-dom": "16.14.0", "react-hot-loader": "4.13.1", "react-i18next": "13.3.1", "react-oidc-context": "3.2.0", "react-redux": "7.2.6", "react-router-dom": "5.2.0", "react-select": "1.2.1", "redux": "3.7.2", "redux-cache": "0.3.0", "redux-form": "8.3.10", "redux-thunk": "2.4.2", "rollbar": "2.26.4", "rollbar-sourcemap-webpack-plugin": "3.3.0", "semver": "7.6.3", "source-map-support": "0.5.21", "stack-trace": "0.0.10", "sudo-prompt": "9.2.1", "sundial": "1.7.5", "tidepool-platform-client": "0.61.0", "uuid": "9.0.1", "webmtp": "0.3.3"}, "browserslist": "last 2 electron major versions", "bin": {"electron": "./node_modules/.bin/electron"}, "devDependencies": {"@babel/core": "7.26.0", "@babel/eslint-parser": "7.26.5", "@babel/eslint-plugin": "7.25.9", "@babel/plugin-proposal-class-properties": "7.18.6", "@babel/plugin-proposal-decorators": "7.25.9", "@babel/plugin-proposal-do-expressions": "7.25.9", "@babel/plugin-proposal-export-default-from": "7.25.9", "@babel/plugin-proposal-export-namespace-from": "7.18.9", "@babel/plugin-proposal-function-bind": "7.25.9", "@babel/plugin-proposal-function-sent": "7.25.9", "@babel/plugin-proposal-json-strings": "7.18.6", "@babel/plugin-proposal-logical-assignment-operators": "7.20.7", "@babel/plugin-proposal-nullish-coalescing-operator": "7.18.6", "@babel/plugin-proposal-numeric-separator": "7.18.6", "@babel/plugin-proposal-optional-chaining": "7.21.0", "@babel/plugin-proposal-pipeline-operator": "7.25.9", "@babel/plugin-proposal-throw-expressions": "7.25.9", "@babel/plugin-syntax-dynamic-import": "7.8.3", "@babel/plugin-syntax-import-meta": "7.10.4", "@babel/plugin-transform-classes": "7.25.9", "@babel/plugin-transform-react-constant-elements": "7.25.9", "@babel/plugin-transform-react-inline-elements": "7.25.9", "@babel/polyfill": "7.12.1", "@babel/preset-env": "7.26.0", "@babel/preset-react": "7.26.3", "@babel/register": "7.25.9", "@babel/runtime-corejs2": "7.26.0", "@electron/notarize": "2.5.0", "@kayahr/jest-electron-runner": "29.15.0", "@tidepool/direct-io": "3.0.2", "aws-sdk": "2.1692.0", "babel-jest": "29.7.0", "babel-loader": "8.2.5", "babel-plugin-add-module-exports": "1.0.4", "babel-plugin-dev-expression": "0.2.2", "babel-plugin-module-resolver": "5.0.2", "babel-plugin-rewire": "1.2.0", "babel-plugin-transform-define": "2.1.4", "babel-plugin-transform-react-remove-prop-types": "0.4.24", "chai": "4.4.1", "concurrently": "9.1.2", "copy-webpack-plugin": "12.0.2", "cross-env": "7.0.3", "css-loader": "7.1.2", "difflet": "1.0.1", "drivelist": "12.0.2", "electron": "31.1.0", "electron-builder": "26.0.11", "electron-devtools-installer": "3.2.0", "enzyme": "3.11.0", "eslint": "8.56.0", "eslint-config-airbnb": "18.2.1", "eslint-formatter-pretty": "5.0.0", "eslint-import-resolver-webpack": "0.13.10", "eslint-plugin-import": "2.31.0", "eslint-plugin-jest": "28.10.0", "eslint-plugin-jsx-a11y": "6.10.2", "eslint-plugin-lodash": "8.0.0", "eslint-plugin-promise": "7.2.1", "eslint-plugin-react": "7.37.4", "file-loader": "6.2.0", "flux-standard-action": "2.1.2", "ftp": "0.3.10", "html-webpack-plugin": "5.6.3", "jest": "29.7.0", "json-loader": "0.5.7", "less": "4.2.2", "less-loader": "12.2.0", "mini-css-extract-plugin": "2.9.2", "moment": "2.30.1", "node-loader": "2.1.0", "object-invariant-test-helper": "0.1.1", "optimize-css-assets-webpack-plugin": "6.0.1", "optional": "0.1.4", "redux-mock-store": "1.5.5", "salinity": "0.0.11", "sinon": "19.0.2", "sinon-chai": "4.0.0", "style-loader": "4.0.0", "terser-webpack-plugin": "5.3.11", "url-loader": "4.1.1", "webpack": "5.97.1", "webpack-bundle-analyzer": "4.10.2", "webpack-cli": "6.0.1", "webpack-dev-middleware": "7.4.2", "webpack-dev-server": "5.2.0", "webpack-merge": "6.0.1", "xmlbuilder": "15.1.1", "yup": "0.32.9"}, "devEngines": {"node": ">=7.9.x", "npm": ">=3.x"}, "resolutions": {"charm": "1.0.2", "node-gyp": "^8.4.0"}, "jest": {"moduleNameMapper": {"\\.(jpg|jpeg|png|gif|eot|otf|webp|svg|ttf|woff|woff2|mp4|webm|wav|mp3|m4a|aac|oga)$": "<rootDir>/__mocks__/fileMock.js", "\\.(css|less)$": "identity-obj-proxy"}, "moduleFileExtensions": ["js", "jsx", "json"], "transform": {"^.+\\.js$": "babel-jest"}, "verbose": true, "testMatch": ["**/test/(app|lib)/**/*.js"], "transformIgnorePatterns": ["../node_modules/(?!(webmtp|uuid)/)", "platform-client"], "modulePathIgnorePatterns": ["<rootDir>/app"], "moduleDirectories": ["node_modules", "app"], "runner": "@kayahr/jest-electron-runner", "testEnvironment": "@kayahr/jest-electron-runner/environment", "setupFiles": ["<rootDir>/jest.setup.js"]}}