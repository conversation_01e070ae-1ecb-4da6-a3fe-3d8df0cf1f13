[Version]
Signature   = "$Windows NT$"
Class       = USBDevice
ClassGUID   = {88BAE032-5A81-49f0-BC3D-A4FF138216D6}
Provider    = %ManufacturerName%
CatalogFile = WinUSBInstallation.cat
DriverVer   = 10/19/2022,14.0.0.0

; ========== Manufacturer/Models sections ===========

[Manufacturer]
%ManufacturerName% = Standard,NTx86,NTia64,NTamd64

[Standard.NTx86]
%DeviceDesc% = USB_Install,USB\VID_0E8D&PID_201D&MI_00
%DeviceDesc% = USB_Install,USB\VID_05C6&PID_9120 ; Equil
%DeviceDesc% = USB_Install,USB\VID_18D1&PID_2D01 ; Equil in accessory mode

[Standard.NTia64]
%DeviceDesc% = USB_Install,USB\VID_0E8D&PID_201D&MI_00
%DeviceDesc% = USB_Install,USB\VID_05C6&PID_9120
%DeviceDesc% = USB_Install,USB\VID_18D1&PID_2D01

[Standard.NTamd64]
%DeviceDesc% = USB_Install,USB\VID_0E8D&PID_201D&MI_00
%DeviceDesc% = USB_Install,USB\VID_05C6&PID_9120
%DeviceDesc% = USB_Install,USB\VID_18D1&PID_2D01

; =================== Installation ===================

[USB_Install]
Include = winusb.inf
Needs   = WINUSB.NT

[USB_Install.Services]
Include = winusb.inf
Needs   = WINUSB.NT.Services

[USB_Install.HW]
AddReg = Dev_AddReg

[Dev_AddReg]
HKR,,DeviceInterfaceGUIDs,0x10000,"{D252C909-8325-43A8-9235-0169BF676338}"

; =================== Strings ===================

[Strings]
ManufacturerName              = "Tidepool"
ClassName                     = "WinUSB devices"
DeviceDesc = "Tidepool USB driver (WinUSB)"
REG_MULTI_SZ = 0x00010000
