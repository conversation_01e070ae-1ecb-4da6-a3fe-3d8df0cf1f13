; == BSD2 LICENSE ==
; Copyright (c) 2016, Tidepool Project
;
; This program is free software; you can redistribute it and/or modify it under
; the terms of the associated License, which is identical to the BSD 2-Clause
; License as published by the Open Source Initiative at opensource.org.
;
; This program is distributed in the hope that it will be useful, but WITHOUT
; ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS
; FOR A PARTICULAR PURPOSE. See the License for more details.
;
; You should have received a copy of the License along with this program; if
; not, you can obtain one from Tidepool Project at tidepool.org.
; == BSD2 LICENSE ==
;

;    TIDEPOOLVCP.INF
;
;    Copyright 2010, Intelligraphics, Inc.
;    Copyright 2014, Silicon Laboratories
;    Copyright © 2000-2015 Future Technology Devices International Limited
;    Copyright (c) 2007-2015, Prolific Technology Inc.
;    Copyright 2016, Tidepool
;
; Silicon Labs:
;    Installation INF for Silicon Laboratories CP210x device
;;
; Texas Instruments:
; Texas Instruments Driver Installation file for a UMP Device main driver.
; UMP installation file for TIUSB3410 and TIUSB5052
;
; Prolific Technology:
; PL2303 Driver for (for Windows Vista / Win7 / Win8 / Win8.1 / Win10)

[Version]
Signature="$WINDOWS NT$"
DriverPackageType=PlugAndPlay
DriverPackageDisplayName=%DESC%
Class=Ports
ClassGuid={4D36E978-E325-11CE-BFC1-08002BE10318}
Provider=%Provider%
DriverVer=02/27/2023,4.0.0.0
CatalogFile=tidepoolvcp.cat

; ================= Device section =====================

[Manufacturer]
%MfgName%=Tidepool,NTx86,NTamd64

[Tidepool.NTx86]
%USB\VID_10C4&PID_85A7.DeviceDesc%=silabser.NTx86, USB\VID_10C4&PID_85A7 ;Silicon Labs (e.g. VerioIQ)
%USB\VID_22A3&PID_0047.DeviceDesc%=cdcacm.Dev, USB\VID_22A3&PID_0047   ;Windows CDC ACM (e.g. Dexcom)
%USB\VID_0483&PID_5740.DeviceDesc%=cdcacm.Dev, USB\VID_0483&PID_5740
%PID_3410.DeviceDesc%=TI.Uni,USB\VID_1A61&PID_3410 ; Abbott FreeStyle USB Data Cable
%PID_3420.DeviceDesc%=TI.Uni,USB\VID_1A61&PID_3420 ; Abbott Strip Port USB Data Cable
%VID_067B&PID_2303.DeviceDesc% = ComPort.NTx86, USB\VID_067B&PID_2303 ; Prolific PL2303
%VID_067B&PID_2304.DeviceDesc% = ComPort.NTx86, USB\VID_067B&PID_2304 ; Prolific PL2304
%USB\VID_0EFB&PID_0011.DeviceDesc%=silabser.NTx86, USB\VID_0EFB&PID_0011 ; Arkray cable

[Tidepool.NTamd64]
%USB\VID_10C4&PID_85A7.DeviceDesc%=silabser.NTamd64, USB\VID_10C4&PID_85A7
%USB\VID_22A3&PID_0047.DeviceDesc%=cdcacm.Dev, USB\VID_22A3&PID_0047
%USB\VID_0483&PID_5740.DeviceDesc%=cdcacm.Dev, USB\VID_0483&PID_5740
%PID_3410.DeviceDesc%=TI.Uni,USB\VID_1A61&PID_3410 ; Abbott FreeStyle USB Data Cable
%PID_3420.DeviceDesc%=TI.Uni,USB\VID_1A61&PID_3420 ; Abbott Strip Port USB Data Cable
%VID_067B&PID_2303.DeviceDesc% = ComPort.NTamd64, USB\VID_067B&PID_2303 ; Prolific PL2303
%VID_067B&PID_2304.DeviceDesc% = ComPort.NTamd64, USB\VID_067B&PID_2304 ; Prolific PL2304
%USB\VID_0EFB&PID_0011.DeviceDesc%=silabser.NTx86, USB\VID_0EFB&PID_0011 ; Arkray cable

[silabser.NTx86]
AddReg=silabser.AddReg
CopyFiles=silabser.Files.Ext
FeatureScore=0x40

[silabser.NTamd64]
AddReg=silabser.AddReg
CopyFiles=silabser.Files.Ext
FeatureScore=0x40

[silabser.NTx86.Services]
Addservice = silabser,0x00000002,silabser.AddService

[silabser.NTamd64.Services]
Addservice = silabser,0x00000002,silabser.AddService

[cdcacm.Dev.NT]
Include=mdmcpq.inf
Include=msports.inf
AddReg=cdcacm.Addreg

[cdcacm.Dev.NT.Services]
Include=mdmcpq.inf
AddService=usbser,0x00000002,UsbserService
AddService=serenum,,SerenumService

[SerenumService]
DisplayName    = %SerEnum.SvcDesc%
ServiceType    = 1               ; SERVICE_KERNEL_DRIVER
StartType      = 3               ; SERVICE_DEMAND_START
ErrorControl   = 1               ; SERVICE_ERROR_NORMAL
ServiceBinary  = %12%\serenum.sys
LoadOrderGroup = PNP Filter

[UsbserService]
ServiceType    = 1                  ; SERVICE_KERNEL_DRIVER
StartType      = 3                  ; SERVICE_DEMAND_START
ErrorControl   = 1                  ; SERVICE_ERROR_NORMAL
ServiceBinary  = %12%\usbser.sys

[silabser.AddService]
DisplayName    = %silabser.SvcDesc%
ServiceType    = 1                  ; SERVICE_KERNEL_DRIVER
StartType      = 3                  ; SERVICE_DEMAND_START
ErrorControl   = 1                  ; SERVICE_ERROR_NORMAL
ServiceBinary  = %12%\silabser.sys

; common registry entries
[silabser.AddReg]
HKR,,NTMPDriver,,silabser.sys
HKR,,RateLimitPurgeMS, 0x10001, 0x64, 0x00, 0x00, 0x00
HKR,,OverrideDefaultPortSettings, 0x10001, 01,00,00,00
HKR,,InitialBaudRate, 0x10001, 00,C2,01,00		;115200 initial baud rate
HKR,,InitialLineControl,, "8N1"				;8-bits, No parity, 1 stop bit
HKR,,PortSubClass,1,01
HKR,,EnumPropPages32,,"MsPorts.dll,SerialPortPropPageProvider"

[cdcacm.AddReg]
HKR,,EnumPropPages32,,"msports.dll,SerialPortPropPageProvider"
HKR,,"LowerFilters",0x00010000,"usbser"
HKR,,"UpperFilters",0x00010000,"serenum"

[silabser.NTx86.HW]
AddReg=silabser.HW.AddReg

[silabser.NTamd64.HW]
AddReg=silabser.HW.AddReg

[silabser.HW.AddReg]
HKR,,"SelectiveSuspendTimeout",0x00010001,10000

[silabser.Files.Ext]
silabser.sys

[SourceDisksNames.x86]
1=%Disk_Description%,"tidepoolvcp.cat",,\x86
2=%Disk_Description%,,,

[SourceDisksNames.amd64]
1=%Disk_Description%,"tidepoolvcp.cat",,\x64
2=%Disk_Description%,,,

[SourceDisksFiles.x86]
silabser.sys = 2,i386
tiusb.i51=2,i386
tiusb.sys=2,i386
ser2pl.sys=2,i386

[SourceDisksFiles.amd64]
silabser.sys = 2,amd64
tiusb.i51=2,amd64
tiusb.sys=2,amd64
ser2pl64.sys=2,amd64

[DestinationDirs]
Silabser.Files.Ext = 12
DefaultDestDir=12
TI.CopyFiles.NT=12
TI.CopyFiles.NT.Coinstaller = 11
ComPort.NTx86.Copy = 12
ComPort.NTamd64.Copy = 12
CoInstaller_CopyFiles.KMDF.1.11 = 11 ; windows\system32


;-------------- SI Labs WDF Coinstaller installation

[silabser.NTx86.CoInstallers]
AddReg=CoInstaller_AddReg.KMDF.1.11
CopyFiles=CoInstaller_CopyFiles.KMDF.1.11

[silabser.NTamd64.CoInstallers]
AddReg=CoInstaller_AddReg.KMDF.1.11
CopyFiles=CoInstaller_CopyFiles.KMDF.1.11

[CoInstaller_CopyFiles.KMDF.1.11]
WdfCoInstaller01011.dll

[SourceDisksFiles]
WdfCoInstaller01011.dll=1
WdfCoInstaller01009.dll=1

[CoInstaller_AddReg.KMDF.1.11]
HKR,,CoInstallers32,0x00010000, "WdfCoInstaller01011.dll,WdfCoInstaller"

[silabser.NTx86.Wdf]
KmdfService = silabser, silabser_wdfsect.1.11

[silabser.NTamd64.Wdf]
KmdfService = silabser, silabser_wdfsect.1.11

[SiLabser_wdfsect.1.11]
KmdfLibraryVersion = 1.11


;-------------- TI WDF Coinstaller installation
[TI.Uni.NT.CoInstallers]
AddReg=TI.Uni.NT.CoInstallers.AddReg
CopyFiles=TI.CopyFiles.NT.Coinstaller

[TI.CopyFiles.NT.Coinstaller]
WdfCoInstaller01009.dll

[TI.Uni.NT.Wdf]
KmdfService = ABB3410, tiusb_wdfsect

[tiusb_wdfsect]
KmdfLibraryVersion = 1.9

[TI.Uni.NT.CoInstallers.AddReg]
HKR,,CoInstallers32,0x00010000, "WdfCoInstaller01009.dll,WdfCoInstaller"

;------------ Texas Instruments

[TI.Uni.NT]
CopyFiles=TI.CopyFiles.NT,TI.CopyFiles.NT.Coinstaller

[TI.Uni.NT.HW]
AddReg=TI.AddReg.NT.HW

[TI.AddReg.NT.HW]
HKR, Parameters\Wdf, VerboseOn,       0x00010001, 1
HKR, Parameters\Wdf, VerifierOn,      0x00010001, 1
HKR, Parameters\Wdf, DbgBreakOnError, 0x00010001, 1
HKR,,ImageFile,,"\SystemRoot\System32\drivers\tiusb.i51"
HKR,,SetMSRLinesZero,%REG_BINARY%,0x00  ; 0x80=DCD 0x40=RI 0x20=DSR 0x10=CTS
HKR,,SetMSRLinesOne, %REG_BINARY%,0x00  ; 0x80=DCD 0x40=RI 0x20=DSR 0x10=CTS
HKR,,NumComPorts,%REG_DWORD%,1
HKR,,NumLptPorts,%REG_DWORD%,0
HKR,,TIDeviceType,,"TIUSB3410"
HKR,,Port1DeviceHWID,,"VID_0451_com"
; ****
; This defines the name shown by the Found New HW wizard for the COM port child device:
HKR,,ComPortChildDeviceText,,"UMP USB Serial Port"

[TI.CopyFiles.NT]
tiusb.i51
tiusb.sys,,,0x00000004

[TI.Uni.NT.Services]
AddService = ABB3410, 0x00000002, TI.AddService

[TI.AddService]
DisplayName    = %TI.SvcDesc%
ServiceType    = 1                  ; SERVICE_KERNEL_DRIVER
StartType      = 3                  ; SERVICE_DEMAND_START
ErrorControl   = 1                  ; SERVICE_ERROR_NORMAL
ServiceBinary  = %12%\tiusb.sys
LoadOrderGroup = Base

;------------ Prolific Technology

[ComPort.NTx86]
CopyFiles=ComPort.NTx86.Copy
AddReg=ComPort.NTx86.AddReg

[ComPort.NTx86.HW]
AddReg=ComPort.HW.AddReg

[ComPort.NTx86.Services]
AddService = ser2pl, 0x00000002, Serial_Service_Inst.NTx86

[ComPort.NTx86.Copy]
ser2pl.sys

[ComPort.NTx86.AddReg]
HKR,,DevLoader,,*ntkern
HKR,,NTMPDriver,,ser2pl.sys
HKR,,EnumPropPages32,,"MsPorts.dll,SerialPortPropPageProvider"

[ComPort.HW.AddReg]
HKR,,"UpperFilters",0x00010000,"serenum"

[ComPort.NTAMD64]
CopyFiles=ComPort.NTAMD64.Copy
AddReg=ComPort.NTAMD64.AddReg

[ComPort.NTAMD64.HW]
AddReg=ComPort.HW.AddReg

[ComPort.NTAMD64.Services]
AddService = ser2pl64, 0x00000002, Serial_Service_Inst.NTAMD64

[ComPort.NTAMD64.Copy]
ser2pl64.sys

[ComPort.NTAMD64.AddReg]
HKR,,DevLoader,,*ntkern
HKR,,NTMPDriver,,ser2pl64.sys
HKR,,EnumPropPages32,,"MsPorts.dll,SerialPortPropPageProvider"

[Serial_Service_Inst.NTx86]
DisplayName    = %Serial.SVCDESC%
ServiceType    = 1                  ; SERVICE_KERNEL_DRIVER
StartType      = 3                  ; SERVICE_DEMAND_START
ErrorControl   = 1                  ; SERVICE_ERROR_NORMAL
ServiceBinary  = %12%\ser2pl.sys
LoadOrderGroup = Base

[Serial_Service_Inst.NTAMD64]
DisplayName    = %Serial.SVCDESC%
ServiceType    = 1                  ; SERVICE_KERNEL_DRIVER
StartType      = 3                  ; SERVICE_DEMAND_START
ErrorControl   = 1                  ; SERVICE_ERROR_NORMAL
ServiceBinary  = %12%\ser2pl64.sys
LoadOrderGroup = Base


;---------------------------------------------------------------;

[Strings]
Provider="Tidepool"
MfgName="Tidepool"
DESC="Driver Package - VCP Driver"
Disk_Description="Tidepool Tidepool USB Driver Installation Disk"
USB\VID_22A3&PID_0047.DeviceDesc="Tidepool USB Driver (CDC ACM chipset)"
USB\VID_0483&PID_5740.DeviceDesc="Tidepool USB Driver (CDC ACM chipset)"
USB\VID_10C4&PID_85A7.DeviceDesc="Tidepool USB Driver (SL chipset)"
USB\VID_0EFB&PID_0011.DeviceDesc="Tidepool USB Driver (SL chipset)"
PID_3410.DeviceDesc="Tidepool USB Driver (TI chipset)"
PID_3420.DeviceDesc="Tidepool USB Driver (TI chipset)"
VID_067B&PID_2303.DeviceDesc="Tidepool USB Driver (Prolific chipset)"
VID_067B&PID_2304.DeviceDesc="Tidepool USB Driver (Prolific chipset)"

silabser.SvcDesc="Tidepool USB Driver (SILabs chipset)"
PortsClassName = "Ports (COM & LPT)"
SerEnum.SvcDesc="Serenum Filter Driver"
TI.SvcDesc="TI USB Cable Driver"
Serial.SVCDESC  = "Tidepool USB Driver (Prolific chipset)"

REG_BINARY =0x00000001
REG_DWORD  =0x00010001
