; This package adheres to the specifications below.
; Intended System Type: NT (32 and 64 variants)
; WinDDK Version: 7600.16385.1
; Copyright (c) 2015 Roche

[Version]
Signature = "$Windows NT$"
Class = PHDC
ClassGUID = {182A3B42-D570-4066-8D13-C72202B40D78}
Provider = %Manufacturer%
DriverVer = 09/19/2024, 4.0.0.0
CatalogFile = phdc_driver.cat

; ================== Class section ==================

[ClassInstall32]
AddReg=PHDC_Class_Install_Add_Reg

[PHDC_Class_Install_Add_Reg]
HKR,,,0,%ClassName%
HKR,,Icon,,"-20"
;------deny access to built-in guests, deny access to anonymous login, allow read/write/execute to authenticated users, allow full
;------control to administrators
HKR,,Security,,"D:(D;OICI;GA;;;BG)(D;OICI;GA;;;AN)(A;OICI;GRGWGX;;;AU)(A;OICI;GA;;;BA)"

; ========== Manufacturer/Models sections ===========

[Manufacturer]
%Manufacturer% = PHDC_Device_WinUSB,NTx86,NTamd64

[PHDC_Device_WinUSB.NTx86]
%USB\ACGuide.DeviceDesc% = USB_Install, USB\VID_173A&PID_21D5
%USB\AvivaConnect.DeviceDesc% = USB_Install, USB\VID_173A&PID_21CF
%USB\ACGuideMe.DeviceDesc% = USB_Install, USB\VID_173A&PID_21D6
%USB\ACInstant.DeviceDesc% = USB_Install, USB\VID_173A&PID_21D7
%USB\ReliOnPlatinum.DeviceDesc% = USB_Install, USB\VID_173A&PID_21D8
%USB\ACGuideLink.DeviceDesc% = USB_Install, USB\VID_173A&PID_21DB

[PHDC_Device_WinUSB.NTamd64]
%USB\ACGuide.DeviceDesc% = USB_Install, USB\VID_173A&PID_21D5
%USB\AvivaConnect.DeviceDesc% = USB_Install, USB\VID_173A&PID_21CF
%USB\ACGuideMe.DeviceDesc% = USB_Install, USB\VID_173A&PID_21D6
%USB\ACInstant.DeviceDesc% = USB_Install, USB\VID_173A&PID_21D7
%USB\ReliOnPlatinum.DeviceDesc% = USB_Install, USB\VID_173A&PID_21D8
%USB\ACGuideLink.DeviceDesc% = USB_Install, USB\VID_173A&PID_21DB

; =================== Installation ===================

;[1]
[USB_Install]
Include=winusb.inf
Needs=WINUSB.NT

;[2]
[USB_Install.Services]
Include=winusb.inf
AddService=WinUSB,0x00000002,WinUSB_ServiceInstall

;[3]
[WinUSB_ServiceInstall]
DisplayName     = %WinUSB_SvcDesc%
ServiceType     = 1
StartType       = 3
ErrorControl    = 1
ServiceBinary   = %12%\winusb.sys

;[4]
[USB_Install.Wdf]
KmdfService=WINUSB, WinUsb_Install

[WinUSB_Install]
KmdfLibraryVersion=1.9

;[5]
[USB_Install.HW]
AddReg=Dev_AddReg

[Dev_AddReg]
HKR,,DeviceInterfaceGUIDs,0x10000,"{B8B610DE-FB41-40A1-A4D6-AB28E87C5F08}"

;[6]
[USB_Install.CoInstallers]
AddReg=CoInstallers_AddReg
CopyFiles=CoInstallers_CopyFiles

[CoInstallers_AddReg]
HKR,,CoInstallers32,0x00010000,"WdfCoInstaller01009.dll,WdfCoInstaller","WinUSBCoInstaller2.dll"

[CoInstallers_CopyFiles]
WinUSBCoInstaller2.dll
WdfCoInstaller01009.dll

[DestinationDirs]
CoInstallers_CopyFiles=11

; ================= Source Media Section =====================
;[7]

[SourceDisksNames]
1 = %DISK_NAME%,,,\x86
2 = %DISK_NAME%,,,\x64

[SourceDisksFiles.x86]
WinUSBCoInstaller2.dll=1
WdfCoInstaller01009.dll=1

[SourceDisksFiles.amd64]
WinUSBCoInstaller2.dll=2
WdfCoInstaller01009.dll=2


; =================== Strings ===================

[Strings]
Manufacturer="Roche"
USB\ACGuide.DeviceDesc="ACCU-CHEK Guide"
USB\AvivaConnect.DeviceDesc="ACCU-CHEK Aviva Connect"
USB\ACGuideMe.DeviceDesc="ACCU-CHEK Guide Me"
USB\ACInstant.DeviceDesc="ACCU-CHEK Instant"
USB\ReliOnPlatinum.DeviceDesc="ReliOn Platinum"
USB\ACGuideLink.DeviceDesc="ACCU-CHEK Guide Link"
WinUSB_SvcDesc="WinUSB Service"
DISK_NAME="PHDC Driver Install Disk"
ClassName="PHDC"
