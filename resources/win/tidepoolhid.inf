; == BSD2 LICENSE ==
; Copyright (c) 2016, Tidepool Project
;
; This program is free software; you can redistribute it and/or modify it under
; the terms of the associated License, which is identical to the BSD 2-Clause
; License as published by the Open Source Initiative at opensource.org.
;
; This program is distributed in the hope that it will be useful, but WITHOUT
; ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS
; FOR A PARTICULAR PURPOSE. See the License for more details.
;
; You should have received a copy of the License along with this program; if
; not, you can obtain one from Tidepool Project at tidepool.org.
; == BSD2 LICENSE ==
;

; TIDEPOOLHID.INF
;
; Disables power management for Bayer Contour Next meter to enable successful upload on Windows 8 and above

[Version]
Signature   ="$WINDOWS NT$"
Class       =HIDClass
ClassGuid   ={745a17a0-74d3-11d0-b6fe-00a0c90f57da}
Provider    =%VendorName%
DriverVer   =04/21/2016,1.3.0.0
CatalogFile =tidepoolhid.cat

; ================= Class section =====================
[ControlFlags]
ExcludeFromSelect=*

[SourceDisksNames]
1 = %DiskName%,,,""

;*****************************************
; Install Section
;*****************************************

[Manufacturer]
%VendorName% = Tidepool,NTx86,NTamd64

[Tidepool.NTx86]
%BayerHID.DeviceDesc% = BayerHID, USB\VID_1A79&PID_7350

[Tidepool.NTamd64]
%BayerHID.DeviceDesc% = BayerHID, USB\VID_1A79&PID_7350

[BayerHID.NT] 
include = input.inf
needs = HID_Inst.NT

[BayerHID.NT.HW]
include = input.inf
AddReg = BayerHID_AddReg 

[BayerHID_AddReg]
HKR,,"EnhancedPowerManagementEnabled",0x00010001,0

[BayerHID.NT.Services]
include     = input.inf
needs       = HID_Inst.NT.Services

[Strings]
VendorName = "Tidepool"
DiskName   = "Tidepool USB HID Driver Installation Disk"
BayerHID.DeviceDesc = "Bayer Contour Next (HID Device)"
