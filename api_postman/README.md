tidepool-api postman collection
=====

A postman collection that targets the tidepool-api

1. Install postman https://www.getpostman.com/
2. Import the `tidepool_api.json.postman_collection`
3. Import the `production.postman_environment` environment file used in the collection. To do this go to the `Manage Environments` button where you will be given the option to import the environment file.


**note:** you can target other environments by copying the production env and then pointing it to the other env by updating the api endpoint and also user details.

