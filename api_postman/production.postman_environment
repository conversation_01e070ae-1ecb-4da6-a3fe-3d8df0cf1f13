{"id": "234515cc-2f5c-8787-3309-8927a6cc9963", "name": "production", "values": [{"key": "api", "value": "https://api.tidepool.org", "type": "text", "name": "api", "enabled": true}, {"key": "userId", "value": "123", "type": "text", "name": "userId", "enabled": true}, {"key": "userEmail", "value": "<EMAIL>", "type": "text", "name": "userEmail", "enabled": true}, {"key": "sessionToken", "value": "123", "type": "text", "name": "sessionToken", "enabled": true}, {"key": "groupId", "value": "123", "type": "text", "name": "groupId", "enabled": true}, {"key": "loginEmail", "value": "{{userEmail}}", "type": "text", "name": "loginEmail", "enabled": true}, {"key": "loginPw", "value": "123", "type": "text", "name": "loginPw", "enabled": true}, {"key": "signupEmail", "value": "{{userEmail}}", "type": "text", "name": "signupEmail", "enabled": true}, {"key": "signupFullName", "value": "foo bar", "type": "text", "name": "signupFullName", "enabled": true}, {"key": "signupPw", "value": "123", "type": "text", "name": "signupPw", "enabled": true}], "timestamp": 1444955559997, "synced": false, "syncedFilename": ""}