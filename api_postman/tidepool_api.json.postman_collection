{"id": "5037a36b-900b-13af-76d3-c614b548cb6e", "name": "tidepool api", "description": "", "order": ["d47d8915-df7b-ce90-5f5b-13eebe80e07b", "68f12eec-ce47-196a-204c-afa6e636df86"], "folders": [{"id": "b3dc71f2-5377-867b-81d0-cc5fee88e6a9", "name": "download user data", "description": "Tidepool API: Downloading Health Data\n - device data\n - user notes\n - http://developer.tidepool.io/tidepool-api/downloading/", "order": ["ffb58e84-9b11-6e83-78f3-88d11114f43c", "3399183f-b344-a5e3-8adb-b9bda3692037", "313e30c1-c67a-b1d5-de20-62acab04aedd", "59ed801b-8657-a6a0-7db5-af9d860bcb0a"], "owner": 0, "collectionId": "5037a36b-900b-13af-76d3-c614b548cb6e"}, {"id": "8b6f4ad7-18be-aa3b-4e28-b405cda6f2da", "name": "managing user", "description": "Tidepool API: Managing User\n- http://developer.tidepool.io/tidepool-api/manage-user/\n\nFor authentication, maintaining session token and logging out, see the introductory API walkthrough .", "order": ["0b9df6db-f74b-0823-5513-c267c9a91219", "77f2edf6-a4b3-f861-6cfd-470ba6b679bf", "c1755436-286d-4401-898d-bb4b22117cd5", "c97e2d42-b5cb-abad-4459-e379e6cbebce", "a52821bf-e782-0f24-9929-09c80e1340db", "4b42a01e-1598-035d-e5e7-f42ebf032877", "21af5e54-895c-477b-7ea4-56227173491c"], "owner": 0, "collectionId": "5037a36b-900b-13af-76d3-c614b548cb6e"}, {"id": "1a700018-a196-727a-b8e0-cfbe6a80fc27", "name": "sharing", "description": "", "order": ["df63175b-288f-8ddf-fafa-5a7394762796", "f3c2ff17-28f1-c3bc-8e0b-4093183e7068", "a1d1e951-9352-f96c-dcef-8d50d528ae10", "cc0fd523-5f2d-6df9-712d-0d974005e3e3", "c67f0e5f-5d6e-3d38-b7c4-9f0fa50011c2", "7d7a89ec-eded-2fc7-32f7-7d474c87ee79", "a5c3b743-3e64-5963-8bb4-57ef49218a5f", "056a7e8a-1cc9-772e-d1af-13fb718a8acb"], "owner": 0}, {"id": "0ec697cb-bf4a-978e-27a4-0a2ae4f41ba3", "name": "upload user data", "description": "Tidepool API: Uploading Health Data\n - device data\n - user notes\n http://developer.tidepool.io/tidepool-api/uploading/", "order": ["c0023cf7-f0f5-980b-9904-018c1c41ea5b", "9cdfa24b-6962-40c2-fc5a-1c7c6ab55f9c", "cc708e7e-e662-f14f-8953-f60d6d5dafa7"], "owner": 0, "collectionId": "5037a36b-900b-13af-76d3-c614b548cb6e"}], "timestamp": 1444348712360, "owner": 0, "remoteLink": "", "public": false, "requests": [{"id": "056a7e8a-1cc9-772e-d1af-13fb718a8acb", "headers": "Content-Type: application/json\nx-tidepool-session-token: {{sessionToken}}\n", "url": "{{api}}/confirm/accept/invite/{{userId}}/{{inviteUserId}} ", "preRequestScript": "", "pathVariables": {}, "method": "PUT", "data": [], "dataMode": "params", "version": 2, "tests": "", "currentHelper": "normal", "helperAttributes": {}, "time": 1444777733187, "name": "accept invite", "description": "", "collectionId": "5037a36b-900b-13af-76d3-c614b548cb6e", "responses": []}, {"id": "0b9df6db-f74b-0823-5513-c267c9a91219", "headers": "", "url": "{{api}}/confirm/resend/signup/", "preRequestScript": "", "pathVariables": {}, "method": "POST", "data": [], "dataMode": "raw", "version": 2, "tests": "", "currentHelper": "normal", "helperAttributes": {}, "time": 1444691486423, "name": "resend signup confirmation", "description": "", "collectionId": "5037a36b-900b-13af-76d3-c614b548cb6e", "responses": [], "folder": "8b6f4ad7-18be-aa3b-4e28-b405cda6f2da", "rawModeData": ""}, {"id": "21af5e54-895c-477b-7ea4-56227173491c", "headers": "Content-Type: application/json\nx-tidepool-session-token: {{sessionToken}}\n", "url": "{{api}}/metadata/{{userId}}/profile", "preRequestScript": "", "pathVariables": {}, "method": "POST", "data": [], "dataMode": "raw", "version": 2, "tests": "", "currentHelper": "normal", "helperAttributes": {}, "time": 1444955569928, "name": "user signup profile", "description": "", "collectionId": "5037a36b-900b-13af-76d3-c614b548cb6e", "responses": [], "rawModeData": "{\"fullName\": \"{{signupFullName}}\"}"}, {"id": "313e30c1-c67a-b1d5-de20-62acab04aedd", "headers": "Content-Type: application/json\nx-tidepool-session-token: {{sessionToken}}\n", "url": "{{api}}/message/all/{{userId}}", "preRequestScript": "", "pathVariables": {}, "method": "GET", "data": [], "dataMode": "params", "version": 2, "tests": "", "currentHelper": "normal", "helperAttributes": {}, "time": 1444690916993, "name": "notes", "description": "", "collectionId": "5037a36b-900b-13af-76d3-c614b548cb6e", "responses": [], "folder": "b3dc71f2-5377-867b-81d0-cc5fee88e6a9"}, {"id": "3399183f-b344-a5e3-8adb-b9bda3692037", "headers": "x-tidepool-session-token: {{sessionToken}}\nContent-Type: application/json\n", "url": "{{api}}/data/{{userId}}", "preRequestScript": "", "pathVariables": {}, "method": "GET", "data": [], "dataMode": "params", "version": 2, "tests": "", "currentHelper": "normal", "helperAttributes": {}, "time": 1444690605896, "name": "download device data", "description": "", "collectionId": "5037a36b-900b-13af-76d3-c614b548cb6e", "responses": [], "folder": "b3dc71f2-5377-867b-81d0-cc5fee88e6a9"}, {"id": "4b42a01e-1598-035d-e5e7-f42ebf032877", "headers": "Content-Type: application/json\n", "url": "{{api}}/auth/user", "preRequestScript": "", "pathVariables": {}, "method": "POST", "data": [], "dataMode": "raw", "version": 2, "tests": "", "currentHelper": "normal", "helperAttributes": {}, "time": 1444955510493, "name": "user signup", "description": "", "collectionId": "5037a36b-900b-13af-76d3-c614b548cb6e", "responses": [], "rawModeData": "{\"username\":\"{{signupEmail}}\", \"password\":\"{{signupPw}}\", \"emails\":[\"{{signupEmail}}\"]}"}, {"id": "59ed801b-8657-a6a0-7db5-af9d860bcb0a", "headers": "Content-Type: application/json\nx-tidepool-session-token: {{sessionToken}}\n", "url": "{{api}}/message/read/{{messageId}}", "preRequestScript": "", "pathVariables": {}, "method": "GET", "data": [], "dataMode": "params", "version": 2, "tests": "", "currentHelper": "normal", "helperAttributes": {}, "time": 1444785052888, "name": "note", "description": "", "collectionId": "5037a36b-900b-13af-76d3-c614b548cb6e", "responses": []}, {"id": "68f12eec-ce47-196a-204c-afa6e636df86", "headers": "x-tidepool-session-token: {{sessionToken}}\n", "url": "{{api}}/auth/logout", "preRequestScript": "", "pathVariables": {}, "method": "POST", "data": [], "dataMode": "params", "version": 2, "tests": "", "currentHelper": "normal", "helperAttributes": {}, "time": 1444954630923, "name": "user logout", "description": "", "collectionId": "5037a36b-900b-13af-76d3-c614b548cb6e", "responses": []}, {"id": "77f2edf6-a4b3-f861-6cfd-470ba6b679bf", "headers": "", "url": "{{api}}/confirm/accept/signup/{{userId}}/ASbH3dcLutzmDOiYy55BHJtIE8AZOcGx", "preRequestScript": "", "pathVariables": {}, "method": "PUT", "data": [], "dataMode": "raw", "version": 2, "tests": "", "currentHelper": "normal", "helperAttributes": {}, "time": 1444691558798, "name": "accept signup confirmation", "description": "", "collectionId": "5037a36b-900b-13af-76d3-c614b548cb6e", "responses": [], "folder": "8b6f4ad7-18be-aa3b-4e28-b405cda6f2da", "rawModeData": ""}, {"id": "7d7a89ec-eded-2fc7-32f7-7d474c87ee79", "headers": "Content-Type: application/json\nx-tidepool-session-token: {{sessionToken}}\n", "url": "{{api}}/confirm/invitations/{{userId}} ", "preRequestScript": "", "pathVariables": {}, "method": "GET", "data": [], "dataMode": "params", "version": 2, "tests": "", "currentHelper": "normal", "helperAttributes": {}, "time": 1444777598638, "name": "get received invites", "description": "", "collectionId": "5037a36b-900b-13af-76d3-c614b548cb6e", "responses": []}, {"id": "9cdfa24b-6962-40c2-fc5a-1c7c6ab55f9c", "headers": "Content-Type: application/json\nx-tidepool-session-token: {{sessionToken}}\n", "url": "{{api}}/message/edit/{{messageId}}", "preRequestScript": "", "pathVariables": {}, "method": "PUT", "data": [], "dataMode": "raw", "version": 2, "tests": "", "currentHelper": "normal", "helperAttributes": {}, "time": 1444785704221, "name": "note edit", "description": "", "collectionId": "5037a36b-900b-13af-76d3-c614b548cb6e", "responses": [], "rawModeData": "{\n    \"message\": {\n          \"messagetext\" : \"correction - should be this note\"\n        }\n}"}, {"id": "a1d1e951-9352-f96c-dcef-8d50d528ae10", "headers": "Content-Type: application/json\nx-tidepool-session-token: {{sessionToken}}\n", "url": "{{api}}/access/{{groupId}}/{{userId}}", "preRequestScript": "", "pathVariables": {}, "method": "POST", "data": [], "dataMode": "params", "version": 2, "tests": "", "currentHelper": "normal", "helperAttributes": {}, "time": 1444777295295, "name": "set users permissons", "description": "", "collectionId": "5037a36b-900b-13af-76d3-c614b548cb6e", "responses": []}, {"id": "a52821bf-e782-0f24-9929-09c80e1340db", "headers": "Content-Type: application/json\n", "url": "{{api}}/confirm/send/forgot/{{userEmail}}", "preRequestScript": "", "pathVariables": {}, "method": "POST", "data": [], "dataMode": "params", "version": 2, "tests": "", "currentHelper": "normal", "helperAttributes": {}, "time": 1444776572639, "name": "lost password", "description": "", "collectionId": "5037a36b-900b-13af-76d3-c614b548cb6e", "responses": []}, {"id": "a5c3b743-3e64-5963-8bb4-57ef49218a5f", "headers": "Content-Type: application/json\nx-tidepool-session-token: {{sessionToken}}\n", "url": "{{api}}/confirm/dismiss/invite/{{userId}}/{{inviteUserId}} ", "preRequestScript": "", "pathVariables": {}, "method": "PUT", "data": [], "dataMode": "params", "version": 2, "tests": "", "currentHelper": "normal", "helperAttributes": {}, "time": 1444777694026, "name": "dismiss invite", "description": "", "collectionId": "5037a36b-900b-13af-76d3-c614b548cb6e", "responses": []}, {"id": "c0023cf7-f0f5-980b-9904-018c1c41ea5b", "headers": "Content-Type: application/json\nx-tidepool-session-token: {{sessionToken}}\n", "url": "{{api}}/message/send/{{userId}}", "preRequestScript": "", "pathVariables": {}, "method": "POST", "data": [], "dataMode": "raw", "version": 2, "tests": "", "currentHelper": "normal", "helperAttributes": {}, "time": 1444785184327, "name": "note add", "description": "", "collectionId": "5037a36b-900b-13af-76d3-c614b548cb6e", "responses": [], "rawModeData": "{\n    \"message\": {\n        \"guid\": \"{{$guid}}\",\n        \"userid\": \"{{userId}}\",\n        \"groupid\": \"{{userId}}\",\n        \"timestamp\": \"2014-07-15T16:43:39+12:00\",\n        \"messagetext\": \"working??\"\n    }\n}"}, {"id": "c1755436-286d-4401-898d-bb4b22117cd5", "headers": "Content-Type: application/json\nx-tidepool-session-token: {{sessionToken}}\n", "url": "{{api}}/auth/user/{{userId}}", "preRequestScript": "", "pathVariables": {}, "method": "GET", "data": [], "dataMode": "params", "version": 2, "tests": "", "currentHelper": "normal", "helperAttributes": {}, "time": 1444776394289, "name": "user", "description": "", "collectionId": "5037a36b-900b-13af-76d3-c614b548cb6e", "responses": []}, {"id": "c67f0e5f-5d6e-3d38-b7c4-9f0fa50011c2", "headers": "Content-Type: application/json\nx-tidepool-session-token: {{sessionToken}}\n", "url": "{{api}}/confirm/invite/{{userId}} ", "preRequestScript": "", "pathVariables": {}, "method": "GET", "data": [], "dataMode": "params", "version": 2, "tests": "", "currentHelper": "normal", "helperAttributes": {}, "time": 1444777529917, "name": "get sent invites", "description": "", "collectionId": "5037a36b-900b-13af-76d3-c614b548cb6e", "responses": []}, {"id": "c97e2d42-b5cb-abad-4459-e379e6cbebce", "headers": "Content-Type: application/json\nx-tidepool-session-token: {{sessionToken}}\n", "url": "{{api}}/metadata/{{userId}}/profile", "preRequestScript": "", "pathVariables": {}, "method": "GET", "data": [], "dataMode": "params", "version": 2, "tests": "", "currentHelper": "normal", "helperAttributes": {}, "time": 1444776447709, "name": "user profile", "description": "", "collectionId": "5037a36b-900b-13af-76d3-c614b548cb6e", "responses": []}, {"id": "cc0fd523-5f2d-6df9-712d-0d974005e3e3", "headers": "Content-Type: application/json\nx-tidepool-session-token: {{sessionToken}}\n", "url": "{{api}}/confirm/send/invite/{{userId}}", "preRequestScript": "", "pathVariables": {}, "method": "POST", "data": [], "dataMode": "raw", "version": 2, "tests": "", "currentHelper": "normal", "helperAttributes": {}, "time": 1444777422962, "name": "invite to share", "description": "", "collectionId": "5037a36b-900b-13af-76d3-c614b548cb6e", "responses": [], "rawModeData": "{\n  \"email\": \"<EMAIL>\",\n  \"permissions\": [\n    \"view\": {}\n  ]\n}"}, {"id": "cc708e7e-e662-f14f-8953-f60d6d5dafa7", "headers": "Content-Type: application/json\nx-tidepool-session-token: {{sessionToken}}\n", "url": "{{api}}/data/{{userId}}", "preRequestScript": "", "pathVariables": {}, "method": "POST", "data": [], "dataMode": "raw", "version": 2, "tests": "", "currentHelper": "normal", "helperAttributes": {}, "time": 1444775944692, "name": "upload device data", "description": "", "collectionId": "5037a36b-900b-13af-76d3-c614b548cb6e", "responses": [], "rawModeData": "[\n  {\n    \"time\": \"{{$timestamp}}\",\n    \"timezoneOffset\": -420,\n    \"clockDriftOffset\": -579000,\n    \"conversionOffset\": 0,\n    \"deviceTime\": \"2014-09-14T13:21:05\",\n    \"deviceId\": \"Device-123\",\n    \"type\": \"smbg\",\n    \"value\": 106,\n    \"units\": \"mg/dL\",\n    \"subType\": \"linked\",\n    \"payload\": {\n      \"logIndices\": [\n        1\n      ]\n    },\n    \"uploadId\": \"upid_d5f278d59e4a\",\n    \"guid\": \"{{$guid}}\"\n  },\n  {\n    \"time\": \"{{$timestamp}}\",\n    \"timezoneOffset\": -420,\n    \"clockDriftOffset\": -579000,\n    \"conversionOffset\": 0,\n    \"deviceTime\": \"2014-09-14T14:32:09\",\n    \"deviceId\": \"Device-123\",\n    \"type\": \"smbg\",\n    \"value\": 115,\n    \"units\": \"mg/dL\",\n    \"subType\": \"linked\",\n    \"payload\": {\n      \"logIndices\": [\n        2\n      ]\n    },\n    \"uploadId\": \"upid_d5f278d59e4a\",\n    \"guid\": \"{{$guid}}\"\n  }\n]"}, {"id": "d47d8915-df7b-ce90-5f5b-13eebe80e07b", "headers": "Authorization: Basic ****************************************\n", "url": "{{api}}/auth/login", "preRequestScript": "", "pathVariables": {}, "method": "POST", "data": [], "dataMode": "raw", "version": 2, "tests": "", "currentHelper": "basicAuth", "helperAttributes": {"username": "{{loginEmail}}", "password": "{{loginPw}}", "saveToRequest": true, "id": "basic", "timestamp": 1426561539930}, "time": 1444955612088, "name": "user login", "description": "", "collectionId": "5037a36b-900b-13af-76d3-c614b548cb6e", "responses": [], "rawModeData": ""}, {"id": "df63175b-288f-8ddf-fafa-5a7394762796", "headers": "Content-Type: application/json\nx-tidepool-session-token: {{sessionToken}}\n", "url": "{{api}}/access/groups/{{userId}}", "preRequestScript": "", "pathVariables": {}, "method": "GET", "data": [], "dataMode": "params", "version": 2, "tests": "", "currentHelper": "normal", "helperAttributes": {}, "time": 1444779128822, "name": "groups user is a member of", "description": "", "collectionId": "5037a36b-900b-13af-76d3-c614b548cb6e", "responses": []}, {"id": "f3c2ff17-28f1-c3bc-8e0b-4093183e7068", "headers": "Content-Type: application/json\nx-tidepool-session-token: {{sessionToken}}\n", "url": "{{api}}/access/{{groupId}} ", "preRequestScript": "", "pathVariables": {}, "method": "GET", "data": [], "dataMode": "params", "version": 2, "tests": "", "currentHelper": "normal", "helperAttributes": {}, "time": 1444779211816, "name": "users who can access data", "description": "", "collectionId": "5037a36b-900b-13af-76d3-c614b548cb6e", "responses": []}, {"id": "ffb58e84-9b11-6e83-78f3-88d11114f43c", "headers": "x-tidepool-session-token: {{sessionToken}}\nContent-Type: application/json\n", "url": "{{api}}/query/data", "preRequestScript": "", "pathVariables": {}, "method": "POST", "data": [], "dataMode": "raw", "version": 2, "tests": "", "currentHelper": "normal", "helperAttributes": {}, "time": 1444691190286, "name": "query device data", "description": "", "collectionId": "5037a36b-900b-13af-76d3-c614b548cb6e", "responses": [], "folder": "b3dc71f2-5377-867b-81d0-cc5fee88e6a9", "rawModeData": "METAQUERY WHERE emails CONTAINS {{userEmail}} QUERY TYPE IN cbg, smbg, bolus, upload"}]}