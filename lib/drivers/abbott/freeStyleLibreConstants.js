/*
 * == BSD2 LICENSE ==
 * Copyright (c) 2017, Tidepool Project
 *
 * This program is free software; you can redistribute it and/or modify it under
 * the terms of the associated License, which is identical to the BSD 2-Clause
 * License as published by the Open Source Initiative at opensource.org.
 *
 * This program is distributed in the hope that it will be useful, but WITHOUT
 * ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS
 * FOR A PARTICULAR PURPOSE. See the License for more details.
 *
 * You should have received a copy of the License along with this program; if
 * not, you can obtain one from Tidepool Project at tidepool.org.
 * == BSD2 LICENSE ==
 */

export const DEVICE_MODEL_NAME = 'FreeStyle Libre';
export const FSLIBRE_PRO_PRODUCT_ID = 0x3670;

export const KETONE_VALUE_FACTOR = 18.0; // according to specs
export const KETONE_HI = 8.0;
export const KETONE_LO = null; // ketone value cannot be low

export const GLUCOSE_HI = 500;
export const GLUCOSE_LO = 40;

export const COMMAND = {
  INIT_REQUEST_1: 0x04,
  INIT_REQUEST_2: 0x05,
  INIT_REQUEST_3: 0x15,
  INIT_REQUEST_4: 0x01,
  BINARY_REQUEST: 0x0a,
  BINARY_RESPONSE: 0x0b,
  ACK_FROM_DEVICE: 0x0c,
  ACK_FROM_HOST: 0x0d,
  TEXT_REQUEST: 0x21,
  TEXT_RESPONSE: 0x60,
};

export const OP_CODE = {
  GET_DATABASE: 0x31,
  GET_DB_SCHEMA: 0x34,
  COMPRESSED_DATABASE: 0x35,
  GET_DATE_TIME: 0x41,
  SET_DATE_TIME: 0X42,
  GET_CFG_DATA: 0x51,
  GET_CFG_SCHEMA: 0x54,
  SET_COMPRESSION: 0x60,
  FLUSH_BUFFERS: 0x7d,
  ERROR: 0x7e,
};

export const ERROR = {
  OK: 0,
  BAD_PARAMS: 1,
  WRONG_PARAM_NUM: 2,
  CMD_FAILED: 3,
  CRC_ERROR: 4,
  INVALID_ID: 5,
};

export const ERROR_DESCRIPTION = {};
ERROR_DESCRIPTION[ERROR.OK] = 'OK: No Error.';
ERROR_DESCRIPTION[ERROR.BAD_PARAMS] = 'Bad parameter values / out of range!';
ERROR_DESCRIPTION[ERROR.WRONG_PARAM_NUM] = 'Wrong number of parameters!';
ERROR_DESCRIPTION[ERROR.CMD_FAILED] = 'Command failed!';
ERROR_DESCRIPTION[ERROR.CRC_ERROR] = 'Wrong checksum!';
ERROR_DESCRIPTION[ERROR.INVALID_ID] = 'Wrong table ID!';

// for CFG and DB fields
export const FIELD_TYPE = {
  UNSIGNED_DECIMAL: 0, // uint
  UNSIGNED_HEX: 1, // uint (display as hex)
  SIGNED_DECIMAL: 2, // int
  UTF8: 3, // string
  BULK_DATA: 4, // bytes
  CRC32: 10, // uint32
  CRC16: 11, // uint16
};

export const DB_TABLE_ID = {
  GLUCOSE_RESULT: 0,
  RAPID_ACTING_INSULIN: 1,
  HISTORICAL_DATA: 6,
  EVENT: 7,
};

export const DB_RECORD_TYPE = {
  COMMON_HEADER: 255,
  GLUCOSE_KETONE_SERVING: 0,
  GLUCOSE_KETONE_MEAL: 1,
  GLUCOSE_KETONE_CARBS: 2,
  UNLOGGED_INSULIN: 3,
  CONTROL_SOL_TEST: 4,
  TIME_CHANGE_RESULT: 5,
  INSULIN_CALC: 6,
  INSULIN_MANUAL: 7,
  HISTORICAL_DATA: 12,
  RESULT_RECORD_WRAP: 13,
  INSULIN_WRAP: 14,
  HISTORICAL_WRAP: 17,
  ERROR: 32,
  LOW_BATTERY: 33,
  DEAD_BATTERY: 34,
  TIME_CHANGE: 35,
  LOST_TIME: 36,
  INSULIN_SETUP_1: 37,
  INSULIN_SETUP_2: 38,
  INSULIN_SETUP_3: 39,
  INSULIN_SETUP_4: 40,
  RESTORE_CONFIG: 41,
  CLEAR_RESULT_DB: 42,
  CLEAR__SCAN_DB: 43,
  CLEAR_ACTIVATION_DB: 44,
  CLEAR_HISTORICAL_DB: 45,
  USER_TIME_CHANGE: 46,
  CLEAR_EVENT_DB: 47,
  RECOVERY_EVENT: 48,
  MICROCONTROLLER_RESET_EVENT: 49,
  MASKED_MODE_STATUS_EVENT: 50,
  SENSOR_EXPIRED_EVENT: 51,
  EVENT_DATABASE_RECORD_NUMBER_WRAP: 52,
};

export const DB_WRAP_RECORDS = {};
DB_WRAP_RECORDS[DB_RECORD_TYPE.RESULT_RECORD_WRAP] = {
  TABLE_ID: DB_TABLE_ID.GLUCOSE_RESULT,
  CRC_OFFSET: 12,
};
DB_WRAP_RECORDS[DB_RECORD_TYPE.INSULIN_WRAP] = {
  TABLE_ID: DB_TABLE_ID.RAPID_ACTING_INSULIN,
  CRC_OFFSET: 12,
};
DB_WRAP_RECORDS[DB_RECORD_TYPE.HISTORICAL_WRAP] = {
  TABLE_ID: DB_TABLE_ID.HISTORICAL_DATA,
  CRC_OFFSET: 6,
};
DB_WRAP_RECORDS[DB_RECORD_TYPE.EVENT_DATABASE_RECORD_NUMBER_WRAP] = {
  TABLE_ID: DB_TABLE_ID.EVENT,
  CRC_OFFSET: 10,
};

export const DB_FIELD_ID = {
  RECORD_NUMBER: 0,
  TIME_VALID: 7,
  TYPE: 8,
  READER_TIME: 9,
  USER_TIME_OFFSET: 10,
  RESULT: 20,
  RESULT_STATUS: 61,
  TEST_TYPE: 23,
  SMART_TAGS: 24,
  LONG_ACTING_INSULIN_RECORDED: 25,
  RAPID_ACTING_INSULIN: 26,
  MEDICATION: 28,
  EXERCISE: 29,
  TREND: 30,
  RAI_RECORD_POINTER: 32,
  RAI_DATA_FLAG: 33,
  LONG_ACTING_INSULIN: 34,
  GLYCEMIC_ALARM: 35,
  GRAMS_PER_SERVING: 36,
  SERVING_COUNT: 37,
  MEAL_TYPE: 38,
  GRAMS_OF_CARB: 39,
  FOOD_DATA_FLAG: 40,
  FOOD_QUICK_TAG: 41,
  RESULT_OLD_READER_TIME: 42,
  RESULT_OLD_USER_OFFSET: 43,
  RESULT_OLD_VALID: 44,
  DATA_CRC: 49,
  SMART_TAG_0: 50,
  SMART_TAG_1: 51,
  SMART_TAG_2: 52,
  SMART_TAG_3: 53,
  SMART_TAG_4: 54,
  SMART_TAG_5: 55,
  SMART_TAG_CRC: 56,
  RESULT_DATA_QUALITY_ERROR: 57,
  RESULT_UNLOGGED_INSULIN: 58,
  EFFECTIVE_TIME: 59,
  WRAP_RECORD_NUMBER: 60,
  RAI_RESULT: 70,
  SIGNED_OVERRIDE: 71,
  SIGNED_CORRECTION: 72,
  IOB: 73,
  MEAL_INSULIN: 74,
  UNLOGGED_INSULIN: 75,
  TIME_OFFSET: 76,
  INSULIN_CRC: 77,
  MANUAL_RAI_RESULT: 78,
  INSULIN_RECORD_WARP: 79,
  GLUCOSE: 150,
  FIRST_FLAG: 151,
  TIME_CHANGE: 152,
  FOOD_FLAG: 153,
  HISTORICAL_RAPID_ACTING_INSULIN: 154,
  HISTORICAL_CRC: 155,
  HISTORICAL_DATA_QUALITY_ERROR: 156,
  LIFE_COUNTER: 157,
  HISTORICAL_DATA_WRAP: 158,
  ERROR_CODE: 160,
  ERROR_DATA: 161,
  OLD_READER_TIME: 165,
  OLD_USER_OFFSET: 166,
  VALID: 167,
  RTC_COUNTER: 168,
  HIGH_CORRECTION: 203,
  LOW_CORRECTION: 204,
  CORRECTION_FACTOR: 205,
  CALCULATOR_OFF_FLAG: 237,
  FIXED_DOSE_BREAKFAST: 206,
  FIXED_DOSE_LUNCH: 207,
  FIXED_DOSE_DINNER: 208,
  MORNING_CARB_INSULIN: 209,
  MID_DAY_CARB_INSULIN: 210,
  EVENING_CARB_INSULIN: 211,
  NIGHT_CARB_INSULIN: 212,
  MORNING_UNITS: 213,
  MID_DAY_UNITS: 214,
  EVENING_UNITS: 215,
  NIGH_UNITS: 216,
  GRAMS_UNITS: 217,
  CARBS: 218,
  UNITS_TIME_OF_DAY_BLOCK: 219,
  CARB_TIME_OF_DAY_BLOCK: 220,
  MORNING_HIGH_CORRECTION: 221,
  MORNING_LOW_CORRECTION: 222,
  MID_DAY_HIGH_CORRECTION: 223,
  MID_DAY_LOW_CORRECTION: 224,
  EVENING_HIGH_CORRECTION: 225,
  EVENING_LOW_CORRECTION: 226,
  NIGHT_HIGH_CORRECTION: 227,
  NIGHT_LOW_CORRECTION: 228,
  CORRECTION_TIME_OF_DAY_BLOCK: 229,
  MORNING_FACTOR: 230,
  MID_DAY_FACTOR: 231,
  EVENING_FACTOR: 232,
  NIGHT_FACTOR: 233,
  INSULIN_DURATION: 234,
  TARGET_TIME_OF_DAY_BLOCK: 235,
  BOB_SYMBOL: 236,
  PATIENT_CONFIG: 170,
  FACTORY_CONFIG: 171,
  CAL_CONFIG_CLEAR: 172,
  STRIP_COUNT: 239,
  SCAN_COUNTER: 241,
  ACTIVATION_COUNT: 242,
  RECOVERY_ITEM: 175,
  LOW_VOLTAGE: 181,
  LOSS_OF_CLOCK: 182,
  LOSS_OF_LOCK: 183,
  WATCHDOG: 184,
  EXTERNAL_PIN_RESET: 185,
  POWER_ON_RESET: 186,
  JTAG_RESET: 187,
  LOCK_UP_ARM: 188,
  SOFTWARE_RESET: 189,
  MDM_AP_RESET: 190,
  EZ_PORT_RESET: 191,
  STOP_MODE_RESET: 192,
  OLD_MASK_MODE: 193,
  NEW_MASK_MODE: 194,
  PDU_STATE: 195,
  WRITTEN_BY_UI: 196,
  EVENT_WRAP: 197,
  EVENT_CRC: 253,
};

export const CFG_TABLE_ID = {
  METER_FACTORY_CONFIGURATION: 1,
  METER_SETTINGS: 2,
  USER_PATIENT_CONFIGURATION: 3,
  USER_PATIENT_SETTINGS: 4,
  INSULIN_SETTINGS: 5,
  REMINDER_STRING: 6,
  SMART_TAG_NOTES: 7,
  STORED_SENSOR_INFORMATION: 9,
  REMINDER_DATA: 10,
};

export const CFG_FIELD_ID = {
  SYSTEM_TYPE: 1025,
  MARKET_LEVEL: 1026,
  MARKET_SUB_LEVEL: 1027,
  MARKET_PUCK_LEVEL: 1028,
  BRAND_NAME: 1029,
  NUMBER_FORMAT: 1056,
  UNIT_OF_MEASURE: 1057,
  INSULIN_CALC_PRESENT: 1058,
  ALLOWABLE_MEAL_UNIT: 1059,
  GLYCEMIC_RANGE_HIGH: 1067,
  GLYCEMIC_RANGE_LOW: 1068,
  TIME_CONVERSION: 1072,
  FIRST_TIME_STARTUP_DONE: 1106,
  PATIENT_NAME: 1130,
  PATIENT_ID: 1131,
  TARGET_RANGE_LOW: 1132,
  TARGET_RANGE_HIGH: 1133,
  LANGUAGE_SETTING: 1134,
  TIME_FORMAT: 1135,
  INSULIN_DOSE_INCREMENT: 1136,
  MASK_MODE_OPTION: 1137,
  MASK_MODE_REMINDER: 1138,
  MASK_MODE_HOUR: 1139,
  MASK_MODE_MIN: 1140,
  BEEPER_VOLUME: 1160,
  NOTIFICATION_SOUND: 1161,
  NOTIFICATION_VIBE: 1162,
  BUTTON_SOUND: 1163,
  INSULIN_MODE: 1190,
  EASY_DONE_FLAG: 1191,
  ADVANCE_DONE_FLAG: 1192,
  CARB_TYPE: 1193,
  CARBS_PER_SERVING: 1194,
  CARB_RATIO_FLAG: 1195,
  CARB_RATIO_ALL_DAY: 1196,
  CARB_RATIO_MORNING: 1197,
  CARB_RATIO_MIDDAY: 1198,
  CARB_RATIO_EVENING: 1199,
  CARB_RATIO_NIGHT: 1200,
  SERVING_RATIO_FLAG: 1201,
  SERVING_RATIO_ALL_DAY: 1202,
  SERVING_RATIO_MORNING: 1203,
  SERVING_RATIO_MIDDAY: 1204,
  SERVING_RATIO_EVENING: 1205,
  SERVING_RATIO_NIGHT: 1206,
  IOB_ICON_FLAG: 1207,
  INSULIN_DURATION: 1208,
  FIXED_DOSE_BREAKFAST: 1209,
  FIXED_DOSE_LUNCH: 1210,
  FIXED_DOES_DINNER: 1211,
  CORRECTION_FACTORS_REQ: 1212,
  CORRECTION_TYPE: 1213,
  SINGLE_TARGET_RATIO_FLAG: 1214,
  SINGLE_TARGET_RATIO_ALL_DAY: 1215,
  SINGLE_TARGET_RATIO_MORNING: 1216,
  SINGLE_TARGET_RATIO_MIDDAY: 1217,
  SINGLE_TARGET_RATIO_EVENING: 1218,
  SINGLE_TARGET_RATIO_NIGHT: 1219,
  LOW_TARGET_RATIO_FLAG: 1220,
  LOW_TARGET_RATIO_ALL_DAY: 1221,
  LOW_TARGET_RATIO_MORNING: 1222,
  LOW_TARGET_RATIO_MIDDAY: 1223,
  LOW_TARGET_RATIO_EVENING: 1224,
  LOW_TARGET_RATIO_NIGHT: 1225,
  HIGH_TARGET_RATIO_FLAG: 1226,
  HIGH_TARGET_RATIO_ALL_DAY: 1227,
  HIGH_TARGET_RATIO_MORNING: 1228,
  HIGH_TARGET_RATIO_MIDDAY: 1229,
  HIGH_TARGET_RATIO_EVENING: 1230,
  HIGH_TARGET_RATIO_NIGHT: 1231,
  BG_DROP_CORRECTION_RATIO_FLAG: 1232,
  BG_DROP_ALL_DAY: 1233,
  BG_DROP_MORNING: 1234,
  BG_DROP_MIDDAY: 1235,
  BG_DROP_EVENING: 1236,
  BG_DROP_NIGHT: 1237,
  REMINDER_STRING_0: 1260,
  REMINDER_STRING_1: 1261,
  REMINDER_STRING_2: 1262,
  REMINDER_STRING_3: 1263,
  REMINDER_STRING_4: 1264,
  REMINDER_STRING_5: 1265,
  REMINDER_STRING_6: 1266,
  REMINDER_STRING_7: 1267,
  REMINDER_STRING_8: 1268,
  REMINDER_STRING_9: 1269,
  REMINDER_STRING_10: 1270,
  REMINDER_STRING_11: 1271,
  SMART_TAG_0: 1290,
  SMART_TAG_1: 1291,
  SMART_TAG_2: 1292,
  SMART_TAG_3: 1293,
  SMART_TAG_4: 1294,
  SMART_TAG_5: 1295,
  BULK_STORAGE: 1310,
  SENSOR_STATE: 1320,
  SENSOR_UID: 1321,
  PAIRED_FLAG: 1322,
  SENSOR_PUCK_INFO: 1323,
  SENSOR_START_TIME: 1324,
  REMINDER_CUSTOM_STRING_1: 1400,
  REMINDER_CUSTOM_STRING_2: 1401,
  REMINDER_CUSTOM_STRING_3: 1402,
  REMINDER_CUSTOM_STRING_4: 1403,
  REMINDER_CUSTOM_STRING_5: 1404,
  REMINDER_CUSTOM_STRING_6: 1405,
  REMINDER_CUSTOM_STRING_7: 1406,
  REMINDER_CUSTOM_STRING_8: 1407,
  REMINDER_CUSTOM_STRING_9: 1408,
  REMINDER_1_DATA_TYPE: 1415,
  REMINDER_1_DATA_STRING_INDEX: 1416,
  REMINDER_1_DATA_TIME_HOUR: 1417,
  REMINDER_1_DATA_TIME_MINUTE: 1418,
  REMINDER_1_DATA_TIME_SECOND: 1419,
  REMINDER_1_DATA_STATUS: 1475,
  REMINDER_2_DATA_TYPE: 1420,
  REMINDER_2_DATA_STRING_INDEX: 1421,
  REMINDER_2_DATA_TIME_HOUR: 1422,
  REMINDER_2_DATA_TIME_MINUTE: 1423,
  REMINDER_2_DATA_TIME_SECOND: 1424,
  REMINDER_2_DATA_STATUS: 1476,
  REMINDER_3_DATA_TYPE: 1425,
  REMINDER_3_DATA_STRING_INDEX: 1426,
  REMINDER_3_DATA_TIME_HOUR: 1427,
  REMINDER_3_DATA_TIME_MINUTE: 1428,
  REMINDER_3_DATA_TIME_SECOND: 1429,
  REMINDER_3_DATA_STATUS: 1477,
  REMINDER_4_DATA_TYPE: 1430,
  REMINDER_4_DATA_STRING_INDEX: 1431,
  REMINDER_4_DATA_TIME_HOUR: 1432,
  REMINDER_4_DATA_TIME_MINUTE: 1433,
  REMINDER_4_DATA_TIME_SECOND: 1434,
  REMINDER_4_DATA_STATUS: 1478,
  REMINDER_5_DATA_TYPE: 1435,
  REMINDER_5_DATA_STRING_INDEX: 1436,
  REMINDER_5_DATA_TIME_HOUR: 1437,
  REMINDER_5_DATA_TIME_MINUTE: 1438,
  REMINDER_5_DATA_TIME_SECOND: 1439,
  REMINDER_5_DATA_STATUS: 1479,
  REMINDER_6_DATA_TYPE: 1440,
  REMINDER_6_DATA_STRING_INDEX: 1441,
  REMINDER_6_DATA_TIME_HOUR: 1442,
  REMINDER_6_DATA_TIME_MINUTE: 1443,
  REMINDER_6_DATA_TIME_SECOND: 1444,
  REMINDER_6_DATA_STATUS: 1480,
  REMINDER_7_DATA_TYPE: 1445,
  REMINDER_7_DATA_STRING_INDEX: 1446,
  REMINDER_7_DATA_TIME_HOUR: 1447,
  REMINDER_7_DATA_TIME_MINUTE: 1448,
  REMINDER_7_DATA_TIME_SECOND: 1449,
  REMINDER_7_DATA_STATUS: 1481,
  REMINDER_8_DATA_TYPE: 1450,
  REMINDER_8_DATA_STRING_INDEX: 1451,
  REMINDER_8_DATA_TIME_HOUR: 1452,
  REMINDER_8_DATA_TIME_MINUTE: 1453,
  REMINDER_8_DATA_TIME_SECOND: 1454,
  REMINDER_8_DATA_STATUS: 1482,
  REMINDER_9_DATA_TYPE: 1455,
  REMINDER_9_DATA_STRING_INDEX: 1456,
  REMINDER_9_DATA_TIME_HOUR: 1457,
  REMINDER_9_DATA_TIME_MINUTE: 1458,
  REMINDER_9_DATA_TIME_SECOND: 1459,
  REMINDER_9_DATA_STATUS: 1483,
  REMINDER_10_DATA_TYPE: 1460,
  REMINDER_10_DATA_STRING_INDEX: 1461,
  REMINDER_10_DATA_TIME_HOUR: 1462,
  REMINDER_10_DATA_TIME_MINUTE: 1463,
  REMINDER_10_DATA_TIME_SECOND: 1464,
  REMINDER_10_DATA_STATUS: 1484,
  REMINDER_11_DATA_TYPE: 1465,
  REMINDER_11_DATA_STRING_INDEX: 1466,
  REMINDER_11_DATA_TIME_HOUR: 1467,
  REMINDER_11_DATA_TIME_MINUTE: 1468,
  REMINDER_11_DATA_TIME_SECOND: 1469,
  REMINDER_11_DATA_STATUS: 1485,
  REMINDER_12_DATA_TYPE: 1470,
  REMINDER_12_DATA_STRING_INDEX: 1471,
  REMINDER_12_DATA_TIME_HOUR: 1472,
  REMINDER_12_DATA_TIME_MINUTE: 1473,
  REMINDER_12_DATA_TIME_SECOND: 1474,
  REMINDER_12_DATA_STATUS: 1486,
  CONFIG_CRC: 2047,
};

export const RESULT_VALUE_TYPE = {
  GLUCOSE: 0,
  KETONE: 1,
  SCAN: 2,
};

export const COMPRESSION = {
  DISABLED: 0,
  ENABLED: 1,
};

export const COMPRESSION_TYPE = {
  UNCOMPRESSED: 0xfd,
  ZERO_COMPRESSED: 0xff,
};

export const CRC32_TABLE = [
  0x00000000,
  0x04c11db7,
  0x09823b6e,
  0x0d4326d9,
  0x130476dc,
  0x17c56b6b,
  0x1a864db2,
  0x1e475005,
  0x2608edb8,
  0x22c9f00f,
  0x2f8ad6d6,
  0x2b4bcb61,
  0x350c9b64,
  0x31cd86d3,
  0x3c8ea00a,
  0x384fbdbd,
];

export const CRC16_TABLE = [
  0x0000, 0x1189, 0x2312, 0x329b, 0x4624, 0x57ad, 0x6536, 0x74bf,
  0x8c48, 0x9dc1, 0xaf5a, 0xbed3, 0xca6c, 0xdbe5, 0xe97e, 0xf8f7,
  0x1081, 0x0108, 0x3393, 0x221a, 0x56a5, 0x472c, 0x75b7, 0x643e,
  0x9cc9, 0x8d40, 0xbfdb, 0xae52, 0xdaed, 0xcb64, 0xf9ff, 0xe876,
  0x2102, 0x308b, 0x0210, 0x1399, 0x6726, 0x76af, 0x4434, 0x55bd,
  0xad4a, 0xbcc3, 0x8e58, 0x9fd1, 0xeb6e, 0xfae7, 0xc87c, 0xd9f5,
  0x3183, 0x200a, 0x1291, 0x0318, 0x77a7, 0x662e, 0x54b5, 0x453c,
  0xbdcb, 0xac42, 0x9ed9, 0x8f50, 0xfbef, 0xea66, 0xd8fd, 0xc974,
  0x4204, 0x538d, 0x6116, 0x709f, 0x0420, 0x15a9, 0x2732, 0x36bb,
  0xce4c, 0xdfc5, 0xed5e, 0xfcd7, 0x8868, 0x99e1, 0xab7a, 0xbaf3,
  0x5285, 0x430c, 0x7197, 0x601e, 0x14a1, 0x0528, 0x37b3, 0x263a,
  0xdecd, 0xcf44, 0xfddf, 0xec56, 0x98e9, 0x8960, 0xbbfb, 0xaa72,
  0x6306, 0x728f, 0x4014, 0x519d, 0x2522, 0x34ab, 0x0630, 0x17b9,
  0xef4e, 0xfec7, 0xcc5c, 0xddd5, 0xa96a, 0xb8e3, 0x8a78, 0x9bf1,
  0x7387, 0x620e, 0x5095, 0x411c, 0x35a3, 0x242a, 0x16b1, 0x0738,
  0xffcf, 0xee46, 0xdcdd, 0xcd54, 0xb9eb, 0xa862, 0x9af9, 0x8b70,
  0x8408, 0x9581, 0xa71a, 0xb693, 0xc22c, 0xd3a5, 0xe13e, 0xf0b7,
  0x0840, 0x19c9, 0x2b52, 0x3adb, 0x4e64, 0x5fed, 0x6d76, 0x7cff,
  0x9489, 0x8500, 0xb79b, 0xa612, 0xd2ad, 0xc324, 0xf1bf, 0xe036,
  0x18c1, 0x0948, 0x3bd3, 0x2a5a, 0x5ee5, 0x4f6c, 0x7df7, 0x6c7e,
  0xa50a, 0xb483, 0x8618, 0x9791, 0xe32e, 0xf2a7, 0xc03c, 0xd1b5,
  0x2942, 0x38cb, 0x0a50, 0x1bd9, 0x6f66, 0x7eef, 0x4c74, 0x5dfd,
  0xb58b, 0xa402, 0x9699, 0x8710, 0xf3af, 0xe226, 0xd0bd, 0xc134,
  0x39c3, 0x284a, 0x1ad1, 0x0b58, 0x7fe7, 0x6e6e, 0x5cf5, 0x4d7c,
  0xc60c, 0xd785, 0xe51e, 0xf497, 0x8028, 0x91a1, 0xa33a, 0xb2b3,
  0x4a44, 0x5bcd, 0x6956, 0x78df, 0x0c60, 0x1de9, 0x2f72, 0x3efb,
  0xd68d, 0xc704, 0xf59f, 0xe416, 0x90a9, 0x8120, 0xb3bb, 0xa232,
  0x5ac5, 0x4b4c, 0x79d7, 0x685e, 0x1ce1, 0x0d68, 0x3ff3, 0x2e7a,
  0xe70e, 0xf687, 0xc41c, 0xd595, 0xa12a, 0xb0a3, 0x8238, 0x93b1,
  0x6b46, 0x7acf, 0x4854, 0x59dd, 0x2d62, 0x3ceb, 0x0e70, 0x1ff9,
  0xf78f, 0xe606, 0xd49d, 0xc514, 0xb1ab, 0xa022, 0x92b9, 0x8330,
  0x7bc7, 0x6a4e, 0x58d5, 0x495c, 0x3de3, 0x2c6a, 0x1ef1, 0x0f78,
];
