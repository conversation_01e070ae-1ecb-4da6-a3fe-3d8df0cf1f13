#!/usr/bin/env node

var program = require('commander');
var fs = require('fs');
var pako = require('pako');

var driverManager = require('../../../driverManager');
var tandemSimulator = require('../tandemSimulator.js');
var builder = require('../../../objectBuilder.js');
var tandemDriver = require('../tandemDriver');
var api = require('../../../core/api.js');
var config = require('../../../../.config.js');
var pkg = require('./../../../../package.json');

var intro = 'Tandem CLI:';

var SYNC_BYTE = 0x55;

/*
 * Load the given tandem blob and then parse and send the data to the tp-platform
 */
function loadFile(filePath, tz, userid) {
  fs.readFile(filePath, function(error, compressed) {
    console.log(intro, 'Reading', filePath);
    if (error) {
      console.log(intro, 'Error reading Tandem blob', error);
      return;
    }

    console.log('Decompressing..');
    var uncompressed = pako.ungzip(compressed);
    var uncompressedString = pako.ungzip(compressed, { to: 'string' });
    var marker = uncompressed.indexOf(SYNC_BYTE);
    var data = JSON.parse(uncompressedString.slice(0, marker));
    data.bytes = uncompressed.slice(marker);

    var drivers = {'Tandem': tandemDriver};
    var cfg = {
                 filename: filePath,
                 timezone: tz,
                 version: pkg.version,
                 groupId: userid,
                 builder: builder(),
                 api: api,
                 deviceInfo: data.deviceInfo,
              };
    cfg.builder.setDefaults({ deviceId: data.deviceInfo.deviceId });

    let tandem = tandemDriver(cfg);
    tandem.processData(progress => progress, data, (err) => {
      if (err) {
        console.log(intro, 'Failed to process data', err);
        process.exit();
      }

      console.log(intro, 'Num log records:', data.log_records.length);
      tandem.uploadData(progress => progress, data, (err) => {
        if (err) {
          console.log(intro, 'upload error: ', err);
          process.exit();
        }
        console.log('Done!');
        process.exit();
      });
    });
  });
}

/*
 * login to the platform
 */
function login(un, pw, config, cb){
  api.create({
    apiUrl: config.API_URL,
    uploadUrl: config.UPLOAD_URL,
    dataUrl: config.DATA_URL,
    version: 'uploader node CLI tool - Tandem'
  });
  api.init(function() {
    api.user.login({ username: un, password:pw}, cb);
  });
}
/*
 * Our CLI that does the work to load the specified raw csv data
 */

program
  .version('0.0.1')
  .option('-f, --file [path]', 'tandem blob file path')
  .option('-u, --username [user]', 'username')
  .option('-p, --password [pw]', 'password')
  .option('-t, --timezone [tz]', 'named timezone', config.DEFAULT_TIMEZONE)//default is 'America/Los_Angeles'
  .parse(process.argv);

console.log(intro, 'Loading Tandem blob...');

if(program.file && program.username && program.password) {
  if (fs.existsSync(program.file)){

    login(program.username, program.password, config, function(err, data){
      if(err){
        console.log(intro, 'Failed authentication!');
        console.log(err);
        return;
      }
      console.log(intro, 'Loading using the timezone', program.timezone);
      console.log(intro, 'Loading for user ', data.userid);

      loadFile( program.file, program.timezone, data.userid );
      return;
    });
  }else{
    console.log(intro + ' file at [%s] not found', program.tandem);
    program.exit();
  }
}else{
  program.help();
}
