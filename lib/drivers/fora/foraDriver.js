import sundial from 'sundial';
import _ from 'lodash';

import structJs from '../../struct';
import common from '../../commonFunctions';
import glucoRxDriver from '../glucorx/glucoRxDriver';

const struct = structJs();
const isBrowser = typeof window !== 'undefined';
// eslint-disable-next-line no-console
const debug = isBrowser ? require('bows')('ForaDriver') : console.log;

let driver, driverObj, cfg, callback, data;
let raw = []; // meter can send packet in parts, so we have to combine it

const buildPacket = (command, payload = [0, 0, 0, 0]) => {
  const packetlen = driver.PACKET_SIZE;
  const buf = new ArrayBuffer(packetlen);
  const bytes = new Uint8Array(buf);
  let ctr = 0;

  ctr += struct.pack(bytes, ctr, 'bb4Bb', driver.CONTROL.START, command, payload, driver.CONTROL.GW_STOP);

  struct.storeByte(driver.GlucoRx.calculateChecksum(bytes, packetlen), bytes, ctr);

  debug('Sending:', common.bytes2hex(bytes));

  return buf;
};

const handleTimeSync = async (event) => {
  raw = raw.concat(Array.from(new Uint8Array(event.target.value.buffer)));
  debug('Received:', common.bytes2hex(raw));

  const start = raw.indexOf(driver.CONTROL.START);
  if (start >= 0) {
    const end = raw.indexOf(driver.CONTROL.MD_STOP);
    if (raw.length >= (end + 2)) {
      const packet = raw.slice(start, end + 2);
      debug('Packet:', common.bytes2hex(packet));
      raw = []; // reset for next packet
      const parsed = driverObj.extractPacketIntoMessages(packet);

      driver.GlucoRx.verifyChecksum(packet, parsed.crc);

      if (parsed.command === driver.COMMAND.READ_TIME) {
        clearTimeout(cfg.abortTimer);
        const dateTime = sundial.buildTimestamp(driver.GlucoRx.parseDateTime(parsed));

        cfg.deviceInfo.deviceTime = sundial.formatDeviceTime(dateTime);
        debug(`Time on device was previously ${cfg.deviceInfo.deviceTime}`);

        common.checkDeviceTime(
          cfg,
          async (timeErr, serverTime) => {
            if (timeErr) {
              if (timeErr === 'updateTime') {
                cfg.deviceInfo.annotations = 'wrong-device-time';

                cfg.dateTime = {
                  year: _.toInteger(sundial.formatInTimezone(serverTime, cfg.timezone, 'YY')),
                  month: _.toInteger(sundial.formatInTimezone(serverTime, cfg.timezone, 'M')),
                  day: _.toInteger(sundial.formatInTimezone(serverTime, cfg.timezone, 'D')),
                  hours: _.toInteger(sundial.formatInTimezone(serverTime, cfg.timezone, 'H')),
                  minutes: _.toInteger(sundial.formatInTimezone(serverTime, cfg.timezone, 'm')),
                  seconds: 0,
                };
                await setDateTime(cfg);
              } else {
                return callback(timeErr);
              }
            } else {
              await disconnect();
              return callback(null, data);
            }
        });
      }

      if (parsed.command === driver.COMMAND.WRITE_TIME) {
        const newDateTime = driver.GlucoRx.parseDateTime(parsed);
        newDateTime.year -= 2000;

        if (!_.isEqual(cfg.dateTime, newDateTime)) {
          debug('Set date/time:', cfg.dateTime);
          debug('Received date/time:', newDateTime);
          return callback (new Error('Error setting date/time.'));
        }

        await disconnect();
        return callback(null, data);
      }
    }
  }
};

export const init = async (config, incoming, cb) => {
  cfg = config;
  callback = cb;
  data = incoming;

  try {
    cfg.deviceComms.ble.customService = await cfg.deviceComms.ble.server.getPrimaryService('00001523-1212-efde-1523-785feabcd123');
    cfg.deviceComms.ble.customCharacteristic = await cfg.deviceComms.ble.customService.getCharacteristic('00001524-1212-efde-1523-785feabcd123');

    cfg.deviceComms.ble.customCharacteristic.addEventListener('characteristicvaluechanged', handleTimeSync);
    await cfg.deviceComms.ble.customCharacteristic.startNotifications();
    debug('Added time sync listener.');

    driver = glucoRxDriver(cfg);
    driverObj = new driver.GlucoRx(cfg);
  } catch (error) {
    return callback(error);
  }
};

export const syncDateTime = async (cfg) => {
  const bytes = buildPacket(driver.COMMAND.READ_TIME);
  await cfg.deviceComms.ble.customCharacteristic.writeValue(bytes);
};

const setDateTime = async (cfg) => {
  const bytes = [];

  bytes[0] = (cfg.dateTime.day & 0x1F) | ((cfg.dateTime.month & 0x07) << 5);
  bytes[1] = (cfg.dateTime.year << 1) | ((cfg.dateTime.month & 0x0F) >> 3);
  bytes[2] = cfg.dateTime.minutes & 0x3F;
  bytes[3] = cfg.dateTime.hours & 0x1F;

  const packet = buildPacket(driver.COMMAND.WRITE_TIME, bytes);
  await cfg.deviceComms.ble.customCharacteristic.writeValue(packet);
};

export const disconnect = async () => {
  try {
    cfg.deviceComms.ble.customCharacteristic.removeEventListener('characteristicvaluechanged', handleTimeSync);
    await cfg.deviceComms.ble.customCharacteristic.stopNotifications();
    cfg.deviceComms.ble.customCharacteristic = null;
    debug('Stopped custom characteristic.');
  } catch (err) {
    debug('Could not stop custom characteristic.');
  }
};
