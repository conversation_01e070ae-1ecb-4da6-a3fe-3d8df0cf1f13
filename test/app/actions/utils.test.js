/*
 * == BSD2 LICENSE ==
 * Copyright (c) 2014, Tidepool Project
 *
 * This program is free software; you can redistribute it and/or modify it under
 * the terms of the associated License, which is identical to the BSD 2-Clause
 * License as published by the Open Source Initiative at opensource.org.
 *
 * This program is distributed in the hope that it will be useful, but WITHOUT
 * ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS
 * FOR A PARTICULAR PURPOSE. See the License for more details.
 *
 * You should have received a copy of the License along with this program; if
 * not, you can obtain one from Tidepool Project at tidepool.org.
 * == BSD2 LICENSE ==
 */


import _ from 'lodash';
import sinon from 'sinon';
import { expect } from 'chai';

import configureStore from 'redux-mock-store';

import ErrorMessages from '../../../app/constants/errorMessages';
import * as utils from '../../../app/actions/utils';
import { addInfoToError } from '../../../app/utils/errors';
import { __Rewire__ } from '../../../app/actions/utils';

describe('utils', () => {
  describe('makeUploadCb', () => {
    const dispatch = sinon.spy();
    afterEach(() => {
      dispatch.resetHistory();
    });
    const errCode = 'E_DEVICE_UPLOAD';
    const utc = new Date().toISOString();
    const mockStore = configureStore([])({
      // just providing the minimum initial state to avoid errors on these tests
      devices: {
        foo: {
          source: {
            type: 'device',
            driverId: 'bar'
          }
        }
      },
      uploadTargetDevice: 'foo',
      version: '0.100.0'
    });
    __Rewire__('osString', 'BeOS R5.1 (RISC-V)');
    const { getState } = mockStore;
    const fn = utils.makeUploadCb(dispatch, getState, errCode, utc);
    test('should return a function', () => {
      expect(typeof fn).to.equal('function');
    });

    test('the returned function should use the configured errCode on error if error does not have a code', () => {
      const err = new Error('Uh oh...');
      const displayErr = new Error(ErrorMessages[errCode]);

      expect(dispatch.callCount).to.equal(0);
      fn(err);
      expect(dispatch.callCount).to.equal(1);
      const expectedAction = {
        type: 'UPLOAD_FAILURE',
        error: true,
        payload: addInfoToError(displayErr, {
          details: err.message,
          utc: utc,
          name: err.name,
          step: null,
          datasetId: null,
          requestTrace: null,
          code: errCode,
          version: '0.100.0'
        }),
        meta: {
          source: 'USER_VISIBLE',
          metric: {
            eventName: 'Upload Failed',
            properties: {
              type: 'device',
              source: 'bar',
              os: 'BeOS R5.1 (RISC-V)',
              error: displayErr,
              device: undefined,
            }
          }
        }
      };
      const result = dispatch.firstCall.args[0];
      expect(result.payload).to.deep.include({
        message: ErrorMessages[errCode],
        details: err.message,
        name: err.name
      });
      expectedAction.payload = result.payload;
      expectedAction.meta.metric.properties.error = result.payload;
      expect(result).to.deep.equal(expectedAction);
    });

    test('the returned function should use the argument error\'s code when present', () => {
      const err = new Error('Uh oh...');
      const specificErrCode = 'E_MEDTRONIC_UPLOAD';
      err.code = specificErrCode;
      const displayErr = new Error(ErrorMessages[specificErrCode]);

      expect(dispatch.callCount).to.equal(0);
      fn(err);
      expect(dispatch.callCount).to.equal(1);
      const expectedAction = {
        type: 'UPLOAD_FAILURE',
        error: true,
        payload: addInfoToError(displayErr, {
          details: err.message,
          utc: utc,
          name: err.name,
          step: null,
          datasetId: null,
          requestTrace: null,
          code: specificErrCode,
          version: '0.100.0'
        }),
        meta: {
          source: 'USER_VISIBLE',
          metric: {
            eventName: 'Upload Failed',
            properties: {
              type: 'device',
              source: 'bar',
              os: 'BeOS R5.1 (RISC-V)',
              error: displayErr,
              device: undefined,
            }
          }
        }
      };
      const result = dispatch.firstCall.args[0];
      expect(result.payload).to.deep.include({
        message: ErrorMessages[specificErrCode],
        details: err.message,
        name: err.name
      });
      expectedAction.payload = result.payload;
      expectedAction.meta.metric.properties.error = result.payload;
      expect(result).to.deep.equal(expectedAction);
    });
  });

  describe('mergeProfileUpdates', () => {
    const profile = {
      emails: ['<EMAIL>'],
      emailVerified: true,
      fullName: 'Joe',
      patient: {
        birthday: '1980-02-05',
        diagnosisDate: '1990-02-06',
        targetDevices: ['medtronic', 'omnipod'],
        targetTimezone: 'US/Central'
      },
      termsAccepted: '2016-05-09T14:33:59-04:00',
      username: '<EMAIL>'
    };

    test('should merge profile updates', () => {
      var update = {fullName: 'New Joe'};
      expect(utils.mergeProfileUpdates(_.cloneDeep(profile), update)).to.deep.equal({
        emails: ['<EMAIL>'],
        emailVerified: true,
        fullName: 'New Joe',
        patient: {
          birthday: '1980-02-05',
          diagnosisDate: '1990-02-06',
          targetDevices: ['medtronic', 'omnipod'],
          targetTimezone: 'US/Central'
        },
        termsAccepted: '2016-05-09T14:33:59-04:00',
        username: '<EMAIL>'
      });
    });

    test('should merge patient updates', () => {
      var update = {patient: {birthday: '1981-02-05'}};
      expect(utils.mergeProfileUpdates(_.cloneDeep(profile), update)).to.deep.equal({
        emails: ['<EMAIL>'],
        emailVerified: true,
        fullName: 'Joe',
        patient: {
          birthday: '1981-02-05',
          diagnosisDate: '1990-02-06',
          targetDevices: ['medtronic', 'omnipod'],
          targetTimezone: 'US/Central'
        },
        termsAccepted: '2016-05-09T14:33:59-04:00',
        username: '<EMAIL>'
      });
    });

    test('should replace emails array on update', () => {
      var update = {emails:['<EMAIL>']};
      expect(utils.mergeProfileUpdates(_.cloneDeep(profile), update)).to.deep.equal({
        emails: ['<EMAIL>'],
        emailVerified: true,
        fullName: 'Joe',
        patient: {
          birthday: '1980-02-05',
          diagnosisDate: '1990-02-06',
          targetDevices: ['medtronic', 'omnipod'],
          targetTimezone: 'US/Central'
        },
        termsAccepted: '2016-05-09T14:33:59-04:00',
        username: '<EMAIL>'
      });
    });

    test('should replace targetDevices array on update', () => {
      var update = {patient: {targetDevices: ['tandem']}};
      expect(utils.mergeProfileUpdates(_.cloneDeep(profile), update)).to.deep.equal({
        emails: ['<EMAIL>'],
        emailVerified: true,
        fullName: 'Joe',
        patient: {
          birthday: '1980-02-05',
          diagnosisDate: '1990-02-06',
          targetDevices: ['tandem'],
          targetTimezone: 'US/Central'
        },
        termsAccepted: '2016-05-09T14:33:59-04:00',
        username: '<EMAIL>'
      });
    });
  });
});
