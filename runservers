# This file should be run with 'source' (also known as '.' on linux/macs).
#
# It starts all the services for Tidepool on localhost.
# It expects to be run from a top-level directory with checkouts of various
# repositories at the next level down. So something like:
# tidepool/
#     blip/
#     ...
#     tools/
#
# You can use get_current_tidepool_repos.sh to give you this structure, and
# update_current_tidepool_repos.sh to help you keep it up to date.
#
# It also expects mongod to be on the execution path.
# With that setup, if your current directory is tidepool, then
# you can just type:
#     . tools/runservers
# After it runs, the process IDs are stored in TP_PIDS, and
# the command tp_kill will kill all those IDs at once.
#
# The process ID for each task is also stored in TP_taskname (like TP_seagull).
#
# You can kill and restart a particular task with
#     tp_restart taskname
# If you just want to kill it and not restart, just use normal bash commands:
#     kill $TP_seagull
# To start a task again, use tp_taskname:
#     tp_seagull
# You can check if tasks are still running with tp_check.

# This sets things up with the router (styx) on port 8009. So to get status from
# the shoreline, for example, do this:
# $ curl -f -S --get http://localhost:8009/auth/status
# {"up":["mongo"],"down":[]}  [10:25 PM][~/tidepool][]
#
# It takes a few seconds to get things up and hooked together through service discovery.
# If you go too fast, you'll see:
# $ curl -f -S --get http://localhost:8009/auth/status
# curl: (22) The requested URL returned error: 404 Not Found



tp_warmup() {
    nvm use v0.12
    export TP_PIDS=
}

# This can be used to kill the most recently launched set of tasks. Note that it
# accepts a parameter to kill so you can say tp_kill -9 if you want to try
# to make the tasks go away with extreme prejudice (in case they don't shut
# down normally).
tp_kill() {
    kill $1 $TP_PIDS
    sleep 1
    kill $TP_mongo
}

# kills and restarts one of the tasks by name -- ex: tp_restart jellyfish
tp_restart() {
    NAME=TP_${1/-/}    # remove any dashes from the name
    eval TASK=\$${NAME} # and retrieve its PID
    echo "arg $1 -- name $NAME -- task $TASK"
    echo "Restarting $1 -- task  $TASK"
    kill  $TASK
    # remove the task we just killed from the list of PIDs
    export TP_PIDS=${TP_PIDS/ $TASK/}
    sleep 1
    tp_$1
}

tp_check() {
    # generate a list of names from the environment, then iterate over them
    # and see if all the tasks are running
    set |egrep ^TP_ |tr -d "[0-9]='" |grep -v PIDS|while read pid; do
        if kill -0 ${!pid} >/dev/null 2>&1; then
            echo Found ${pid}
        else
            echo Did NOT find ${pid}
        fi
    done
}

tp_start_server() {   # expects servername, start name
    echo "Starting $1"
    pushd $1 > /dev/null || return
    # use the first line if you want individual logs, the 2nd if you want to aggregate them.
    #node $2 2>&1 > ../$1.log &
    node $2 2>&1 >> ../server.log &
    popd > /dev/null
    PIDDY=$!
    echo "Started $1, pid ${PIDDY}"
    export TP_PIDS="$TP_PIDS $PIDDY"
    NAME=TP_${1/-/}    # remove any dashes from the name
    export $NAME=$PIDDY
    sleep 1
}

tp_go_server() {
    REPO="${1}"
    echo "Starting ${REPO}"
    TP_DIR="${PWD}"
    export GOPATH="${TP_DIR}/${REPO}"
    pushd "${GOPATH}/src/github.com/tidepool-org/${REPO}" > /dev/null
	# use the first line if you want individual logs, the 2nd if you want to aggregate them.
	# ./dist/${REPO} > ${TP_DIR}/${REPO}.log 2>&1 &
	./dist/${REPO} >> ${TP_DIR}/server.log 2>&1 &
	PIDDY=${!}
	echo "Started ${REPO}, pid ${PIDDY}"
	export TP_PIDS="${TP_PIDS} ${PIDDY}"
    NAME="TP_${REPO/-/}"    # remove any dashes from the name
    export ${NAME}="${PIDDY}"
	sleep 1
    popd > /dev/null
}

# Everyone
tp_common() {
    export API_SECRET="This is a local API secret for everyone. BsscSHqSHiwrBMJsEGqbvXiuIUPAjQXU"
    export SERVER_SECRET="This needs to be the same secret everywhere. YaHut75NsK1f9UKUXuWqxNN0RUwHFBCy"
    export LONGTERM_KEY="abcdefghijklmnopqrstuvwxyz"
    export DISCOVERY_HOST=localhost:8000
    export PUBLISH_HOST=localhost
    export METRICS_SERVICE="{ \"type\": \"static\", \"hosts\": [{ \"protocol\": \"http\", \"host\": \"localhost:9191\" }] }"
    export USER_API_SERVICE="{ \"type\": \"static\", \"hosts\": [{ \"protocol\": \"http\", \"host\": \"localhost:9107\" }] }"
    export SEAGULL_SERVICE="{ \"type\": \"static\", \"hosts\": [{ \"protocol\": \"http\", \"host\": \"localhost:9120\" }] }"
    export GATEKEEPER_SERVICE="{ \"type\": \"static\", \"hosts\": [{ \"protocol\": \"http\", \"host\": \"localhost:9123\" }] }"
    export SCHEMA_VERSION=3
}

tp_mongo() {
    echo "Starting mongod"
    mongod > server.log 2>&1 &
    export TP_mongo=$!
}

# hakken
tp_hakken() {
    export ANNOUNCE_HOST=localhost
    export PORT=8000
    export DISCOVERY_HEARTBEAT_INTERVAL=10000
    export LOG_HEARTBEATS=false

    tp_start_server hakken coordinator.js
}

# shoreline
tp_shoreline() {
    # Note: config for shoreline is contained in the repo
    # see https://github.com/tidepool-org/shoreline/tree/master/config
    tp_go_server shoreline
}

# hydrophone

tp_hydrophone() {
    # Note: config for hydrophone is contained in the repo
    # see https://github.com/tidepool-org/hydrophone/tree/master/config
    tp_go_server hydrophone
}

# highwater
tp_highwater() {
    export PORT=9191
    export SERVICE_NAME=highwater
    export SALT_DEPLOY=gf78fSEI7tOQQP9xfXMO9HfRyMnW4Sx88Q
    # if you're not testing highwater, you can just turn it off with this:
    export METRICS_APIKEY=""
    export METRICS_UCSF_APIKEY=""
    # if you need highwater to be working, you can comment out those lines
    # and uncomment these:

    # this is a testing key only; do not use in production.
    # export METRICS_APIKEY=28814957ee8160309f522a0bd0f2824de585befb
    # export METRICS_UCSF_APIKEY=28814957ee8160309f522a0bd0f2824de585befb

    # this will let you test direct to highwater at localhost:9191/noauth
    # export NOAUTH=true

    tp_start_server highwater lib/index.js
}

# seagull
tp_seagull() {
    export PORT=9120
    export SERVICE_NAME=seagull-local
    export SALT_DEPLOY=KEWRWBe5yyMnW4SxosfZ2EkbZHkyqJ5f

    tp_start_server seagull lib/index.js
}

# message-api
tp_messageapi() {
    export PORT=9119
    export SERVICE_NAME=message-api-local
    export SALT_DEPLOY=JPX9TAh6VEcta4TnaQMudx4R1G6ie9Wi
    export SERVER_NAME=message-api

    tp_start_server message-api lib/index.js
}

# tide-whisperer
tp_tidewhisperer() {
    # Note: config for tide-whisperer is contained in the repo
    # see https://github.com/tidepool-org/tide-whisperer/tree/master/config
    tp_go_server tide-whisperer
}

# jellyfish
tp_jellyfish() {
    export PORT=9122
    export SERVICE_NAME=jellyfish-local
    export SERVE_STATIC=dist
    # so that we can validate the version of the uploader being used
    export MINIMUM_UPLOADER_VERSION="0.99.0"
    # the _schemaVersion that jellyfish will produce
    export SCHEMA_VERSION=1
    # Encryption salt for raw uploads
    export SALT_DEPLOY=itNkczadZ1EeC9fUWR3LnbKFagtYYLOk

    if [ "$SKIP_BUILD" != "true" ]; then
      echo "Building Jellyfish..."
      pushd jellyfish > /dev/null || return
      npm run build > /dev/null
      popd > /dev/null
    fi

    tp_start_server jellyfish app.js
}

# gatekeeper
tp_gatekeeper() {
    export PORT=9123
    export SERVICE_NAME=gatekeeper-local
    export GATEKEEPER_SECRET="This secret is used to encrypt the groupId stored in the DB for gatekeeper"

    tp_start_server gatekeeper index.js
}

# styx
tp_styx() {
    export HTTP_PORT=8009
    export RULES=`cat tools/styx_rules.json`
    export DISCOVERY="\
    {\
      \"host\": \"localhost:8000\"\
    }"
    export HTTPS_PORT=8010
    export HTTPS_CONFIG="\
    {\
      \"key\": \"../tools/keys/sslKey.pem\",\
      \"cert\": \"../tools/keys/sslCert.pem\"\
    }"

    tp_start_server styx server.js
    unset HTTPS_PORT
    unset HTTPS_CONFIG
}

# blip
tp_blip() {
    nvm use v6
    export PORT=3000
    export MOCK=false
    export API_HOST="http://localhost:8009"
    export UPLOAD_API="http://tidepool.org/uploader"

    if [ "$SKIP_BUILD" != "true" ]; then
      echo "Building Blip..."
      pushd blip > /dev/null || return
      npm run build > /dev/null
      popd > /dev/null
    fi

    tp_start_server blip server.js
    echo "Blip started at http://localhost:$PORT"
}

# platform
tp_platform_server() {
    echo "Starting $1"
    export GOPATH=${PWD}/platform
    pushd $GOPATH/src/github.com/tidepool-org/platform > /dev/null
    . ./env.sh
    _bin/services/$1/$1 >> ${PWD}/platform_$1_server.log 2>&1 &
	PIDDY=$!
	echo "Started $1, pid ${PIDDY}"
	export TP_PIDS="$TP_PIDS $PIDDY"
    NAME=TP_$1
    export $NAME=$PIDDY
    popd > /dev/null
}

# platform_auth
tp_platform_auth() {
    tp_platform_server auth
}

# platform_data
tp_platform_data() {
    tp_platform_server data
}

# platform_notification
tp_platform_notification() {
    tp_platform_server notification
}

# platform_task
tp_platform_task() {
    tp_platform_server task
}

# platform_user
tp_platform_user() {
    tp_platform_server user
}

# NOTE: If you modify this list, please also modify required_repos.txt.

tp_warmup
tp_mongo
tp_common
tp_hakken || return
tp_shoreline || return
tp_highwater || return
tp_seagull || return
tp_hydrophone || return
tp_messageapi || return
tp_tidewhisperer || return
tp_gatekeeper || return
tp_jellyfish || return
tp_platform_auth || return
tp_platform_data || return
tp_platform_notification || return
tp_platform_task || return
tp_platform_user || return
tp_styx || return
tp_blip || return
